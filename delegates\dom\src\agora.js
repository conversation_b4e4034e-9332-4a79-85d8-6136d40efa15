import AgoraRTC from "agora-rtc-sdk-ng";
// import { SpatialAudioExtension, } from "./spatialAudio/index.esm.js";

import axios from "axios";

AgoraRTC.enableLogUpload();

// const extension = new SpatialAudioExtension();
// AgoraRTC.registerExtensions([extension]);


var client = null

var localTracks = {
  audioTrack: null
};

var remoteUsers = {};
var currentMic = null;
var mics = []

var options = {};

AgoraRTC.onAutoplayFailed = () => {
  alert("click to start autoplay!");
};

AgoraRTC.onMicrophoneChanged = async changedDevice => {
  // When plugging in a device, switch to a device that is newly plugged in.
  if (changedDevice.state === "ACTIVE") {
    localTracks.audioTrack.setDevice(changedDevice.device.deviceId);
    // Switch to an existing device when the current device is unplugged.
  } else if (changedDevice.device.label === localTracks.audioTrack.getTrackLabel()) {
    const oldMicrophones = await AgoraRTC.getMicrophones();
    oldMicrophones[0] && localTracks.audioTrack.setDevice(oldMicrophones[0].deviceId);
  }
};


// $(".mic-list").change(function (e) {
//   switchMicrophone(this.value);
// })



// export async function step_subscribe(){
//   // const uid = $("#remote-uid").val()
//   let remoteUser = remoteUsers[uid]
//   if (!remoteUser) {
//     return;
//   }
//   // const checked = $("#audio-check").prop("checked")
//   // if (checked) {
//     subscribe(remoteUser, "audio");
//   // }
// }




async function switchMicrophone(label) {
  currentMic = mics.find(mic => mic.label.split(" ").join("") === label);
  // switch device of local audio track.
  await localTracks.audioTrack.setDevice(currentMic.deviceId);
}


async function subscribe(user, mediaType) {
  const uid = user.uid;
  // subscribe to a remote user
  await client.subscribe(user, mediaType);
  if (mediaType === 'audio') {
    // user.audioTrack.play();

    // const processor = extension.createProcessor();
    // user.processor = processor;
    const track = user.audioTrack;
    // track.pipe(processor).pipe(track.processorDestination);
    track.play();
  


  }
  // if (!$(`#player-wrapper-${uid}`).length) {
  //   const player = $(`<div id="player-wrapper-${uid}">uid: ${uid}</div>`)
  //   $("#remote-audio").append(player);
  // }
}

function handleUserPublished(user, mediaType) {
  const id = user.uid;
  remoteUsers[id] = user;
  subscribe(user, mediaType)
  // $("#remote-uid").val(id)
}

function handleUserUnpublished(user, mediaType) {
  if (mediaType === 'audio') {
    const id = user.uid;
    delete remoteUsers[id];
    client.unsubscribe(user)
    // $(`#player-wrapper-${id}`).remove();
  }
}

function fetchToken(uid, channelName, tokenRole) {

  return new Promise(function (resolve) {
      axios.post('https://9ds4fj4565.execute-api.us-east-1.amazonaws.com/Prod/token/', {
          uid: uid,
          channelName: channelName,
          role: tokenRole
      }, {
          headers: {
              'Content-Type': 'application/json; charset=UTF-8'
          }
      })
          .then(function (response) {
              const token = response.data.token;
              console.log("token" ,token)
              resolve(token);
          })
          .catch(function (error) {
              console.log(error);
          });
  })
}




export class Agora{
  constructor(){

  }
  async initializeToken(){

  }
  async join(channel, uid){

   

    client = AgoraRTC.createClient({
      mode: "rtc",
      codec: "vp8"
    });

    client.on("user-published", handleUserPublished);
    client.on("user-unpublished", handleUserUnpublished);

    // start Proxy if needed
    // const mode = Number(options.proxyMode)
    // if (mode != 0 && !isNaN(mode)) {
    //   client.startProxyServer(mode);
    // }

    try{

      options.appId = "0b2cc4652ec146059938c704c9133b30";
      options.channel = channel;
      options.uid = uid;
      options.token = await fetchToken(uid, channel, 1);

      
        
      options.uid = await client.join(options.appId, options.channel, options.token, options.uid || null)
      
    } catch (error) {
      // message.error(error.message)
      console.error(error);
    }



    localTracks.audioTrack = await AgoraRTC.createMicrophoneAudioTrack({
      encoderConfig: "music_standard"
    })

   


    // publish local tracks to channel
    await client.publish(Object.values(localTracks));


    mics = await AgoraRTC.getMicrophones();
    // $(".mic-list").empty();
    // mics.forEach(mic => {
    //   const value = mic.label.split(" ").join("")
    //   $(".mic-list").append(`<option value=${value}>${mic.label}</option>`);
    // });
  
    const audioTrackLabel = localTracks.audioTrack.getTrackLabel();
    currentMic = mics.find(item => item.label === audioTrackLabel);
    // $(".mic-list").val(currentMic.label.split(" ").join(""));





  }
  async setMicInput(isOn){

    if(localTracks.audioTrack){
      await localTracks.audioTrack.setMuted(!isOn)
    }

    
  }
  async updateSpatialPos(pos){
      // extension.updateSelfPosition(pos.x, pos.y, pos.z);
  }
  async updateSpatialOriantation(){
    // extension.updateSpatialOrientation
  }
  async setVolume(v){
    // localTracks.audioTrack.setVolume()
    for (const id in remoteUsers) {
      var track = remoteUsers[id].audioTrack;
      if (track) {
        track.setVolume(v)

      }
    }
  }
  async leave(){
    for (const trackName in localTracks) {
      var track = localTracks[trackName];
      if (track) {
        track.stop();
        track.close();
        localTracks[trackName] = undefined;
      }
    }
    // Remove remote users and player views.
    remoteUsers = {};
    // clear remote players views
    // $("#remote-audio").html("");
  
    // leave the channel
    if(client){
      await client.leave();
      console.log("client leaves channel success");
    }
    
  }
}