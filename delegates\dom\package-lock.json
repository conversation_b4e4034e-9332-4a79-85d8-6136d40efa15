{"name": "sps-delegate-dom", "version": "0.1.0", "lockfileVersion": 2, "requires": true, "packages": {"": {"name": "sps-delegate-dom", "version": "0.1.0", "license": "MIT", "dependencies": {"@types/bootstrap": "^5.2.10", "@voxeet/voxeet-web-sdk": "^3.11.0-beta.1", "agora-rtc-sdk-ng": "^4.21.0", "axios": "^1.7.2", "cross-env": "^7.0.3", "nipplejs": "^0.10.1"}, "devDependencies": {"@tensorworks/libspsfrontend": "0.1.4", "@types/jquery": "^3.5.14", "bootstrap": "^5.3.0", "css-loader": "^6.2.0", "dotenv-webpack": "^8.1.0", "html-loader": "^4.1.0", "html-webpack-plugin": "^5.3.2", "image-webpack-loader": "^8.1.0", "mini-css-extract-plugin": "^2.1.0", "open-cli": "^7.0.0", "style-loader": "^3.2.1", "ts-loader": "^9.2.4", "typescript": "^4.3.5", "webpack": "^5.46.0", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.7.4"}}, "node_modules/@agora-js/media": {"version": "4.21.0", "resolved": "https://registry.npmjs.org/@agora-js/media/-/media-4.21.0.tgz", "integrity": "sha512-X4aV84/gB4O7GOOkwP3+NGTHtT2IVkpa4DFBTlBNl7hrkjDwUeanzCQZyva92Zyw59N0NI6dKh9CjJKoIL5Now==", "dependencies": {"@agora-js/report": "4.21.0", "@agora-js/shared": "4.21.0", "agora-rte-extension": "^1.2.4", "axios": "^1.6.8", "pako": "^2.1.0", "webrtc-adapter": "8.2.0"}}, "node_modules/@agora-js/media/node_modules/sdp": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/sdp/-/sdp-3.2.0.tgz", "integrity": "sha512-d7wDPgDV3DDiqulJjKiV2865wKsJ34YI+NDREbm+FySq6WuKOikwyNQcm+doLAZ1O6ltdO0SeKle2xMpN3Brgw=="}, "node_modules/@agora-js/media/node_modules/webrtc-adapter": {"version": "8.2.0", "resolved": "https://registry.npmjs.org/webrtc-adapter/-/webrtc-adapter-8.2.0.tgz", "integrity": "sha512-umxCMgedPAVq4Pe/jl3xmelLXLn4XZWFEMR5Iipb5wJ+k1xMX0yC4ZY9CueZUU1MjapFxai1tFGE7R/kotH6Ww==", "dependencies": {"sdp": "^3.0.2"}, "engines": {"node": ">=6.0.0", "npm": ">=3.10.0"}}, "node_modules/@agora-js/report": {"version": "4.21.0", "resolved": "https://registry.npmjs.org/@agora-js/report/-/report-4.21.0.tgz", "integrity": "sha512-c8KIdomuPItwvri431z5CPT7L4m+jLJrwWWt/QS3JN+sp/t49NnoOIyKQf3y5xCbyNPCNNeGofrwkaIRu4YE8g==", "dependencies": {"@agora-js/shared": "4.21.0", "axios": "^1.6.8"}}, "node_modules/@agora-js/shared": {"version": "4.21.0", "resolved": "https://registry.npmjs.org/@agora-js/shared/-/shared-4.21.0.tgz", "integrity": "sha512-oqaiuIhG9ai/NXUDEmj9b3uGxxU9I0OZZszNaJexjakJuVZPM7ypzrCLUi5SzkTh++QOf68yuvD488fjq5WQsA==", "dependencies": {"axios": "^1.6.8", "ua-parser-js": "^0.7.34"}}, "node_modules/@babel/code-frame": {"version": "7.14.5", "dev": true, "license": "MIT", "dependencies": {"@babel/highlight": "^7.14.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.14.9", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/highlight": {"version": "7.14.5", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.14.5", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@discoveryjs/json-ext": {"version": "0.5.3", "dev": true, "license": "MIT", "engines": {"node": ">=10.0.0"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.2", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/set-array": "^1.0.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.9"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/set-array": {"version": "1.1.2", "dev": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/source-map": {"version": "0.3.2", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/gen-mapping": "^0.3.0", "@jridgewell/trace-mapping": "^0.3.9"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.4.14", "dev": true, "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.15", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.0.3", "@jridgewell/sourcemap-codec": "^1.4.10"}}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@popperjs/core": {"version": "2.11.8", "resolved": "https://registry.npmjs.org/@popperjs/core/-/core-2.11.8.tgz", "integrity": "sha512-P1st0aksCrn9sGZhp8GMYwBnQsbvAWsZAX44oXNNvLHGqAOcoVxmjZiohstwQ7SqKnbR47akdNi+uleWD8+g6A==", "funding": {"type": "opencollective", "url": "https://opencollective.com/popperjs"}}, "node_modules/@sindresorhus/is": {"version": "0.7.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/@tensorworks/libspsfrontend": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/@tensorworks/libspsfrontend/-/libspsfrontend-0.1.4.tgz", "integrity": "sha512-EBLJynDpr3qQrzvnk0Kg/BQY4/VrscGgYcDP3ryvsGzHJBTV1kYp1lT0WROJG2E4WBDt5C976TU0R7Ll3FX34g==", "dev": true, "dependencies": {"events": "^3.3.0"}}, "node_modules/@tokenizer/token": {"version": "0.3.0", "dev": true, "license": "MIT"}, "node_modules/@trysound/sax": {"version": "0.2.0", "dev": true, "license": "ISC", "optional": true, "engines": {"node": ">=10.13.0"}}, "node_modules/@types/body-parser": {"version": "1.19.2", "dev": true, "license": "MIT", "dependencies": {"@types/connect": "*", "@types/node": "*"}}, "node_modules/@types/bonjour": {"version": "3.5.10", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/bootstrap": {"version": "5.2.10", "resolved": "https://registry.npmjs.org/@types/bootstrap/-/bootstrap-5.2.10.tgz", "integrity": "sha512-F2X+cd6551tep0MvVZ6nM8v7XgGN/twpdNDjqS1TUM7YFNEtQYWk+dKAnH+T1gr6QgCoGMPl487xw/9hXooa2g==", "license": "MIT", "dependencies": {"@popperjs/core": "^2.9.2"}}, "node_modules/@types/connect": {"version": "3.4.35", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/connect-history-api-fallback": {"version": "1.3.5", "dev": true, "license": "MIT", "dependencies": {"@types/express-serve-static-core": "*", "@types/node": "*"}}, "node_modules/@types/eslint": {"version": "7.28.0", "dev": true, "license": "MIT", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}}, "node_modules/@types/eslint-scope": {"version": "3.7.1", "dev": true, "license": "MIT", "dependencies": {"@types/eslint": "*", "@types/estree": "*"}}, "node_modules/@types/estree": {"version": "0.0.50", "dev": true, "license": "MIT"}, "node_modules/@types/express": {"version": "4.17.13", "dev": true, "license": "MIT", "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.18", "@types/qs": "*", "@types/serve-static": "*"}}, "node_modules/@types/express-serve-static-core": {"version": "4.17.28", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*"}}, "node_modules/@types/glob": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"@types/minimatch": "*", "@types/node": "*"}}, "node_modules/@types/html-minifier-terser": {"version": "5.1.2", "dev": true, "license": "MIT"}, "node_modules/@types/http-proxy": {"version": "1.17.8", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/jquery": {"version": "3.5.14", "resolved": "https://registry.npmjs.org/@types/jquery/-/jquery-3.5.14.tgz", "integrity": "sha512-X1gtMRMbziVQkErhTQmSe2jFwwENA/Zr+PprCkF63vFq+Yt5PZ4AlKqgmeNlwgn7dhsXEK888eIW2520EpC+xg==", "dev": true, "dependencies": {"@types/sizzle": "*"}}, "node_modules/@types/json-schema": {"version": "7.0.9", "dev": true, "license": "MIT"}, "node_modules/@types/mime": {"version": "1.3.2", "dev": true, "license": "MIT"}, "node_modules/@types/minimatch": {"version": "3.0.5", "dev": true, "license": "MIT"}, "node_modules/@types/minimist": {"version": "1.2.2", "dev": true, "license": "MIT"}, "node_modules/@types/node": {"version": "16.7.10", "dev": true, "license": "MIT"}, "node_modules/@types/normalize-package-data": {"version": "2.4.1", "dev": true, "license": "MIT"}, "node_modules/@types/qs": {"version": "6.9.7", "dev": true, "license": "MIT"}, "node_modules/@types/range-parser": {"version": "1.2.4", "dev": true, "license": "MIT"}, "node_modules/@types/retry": {"version": "0.12.1", "dev": true, "license": "MIT"}, "node_modules/@types/serve-index": {"version": "1.9.1", "dev": true, "license": "MIT", "dependencies": {"@types/express": "*"}}, "node_modules/@types/serve-static": {"version": "1.13.10", "dev": true, "license": "MIT", "dependencies": {"@types/mime": "^1", "@types/node": "*"}}, "node_modules/@types/sizzle": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/@types/sizzle/-/sizzle-2.3.3.tgz", "integrity": "sha512-JYM8x9EGF163bEyhdJBpR2QX1R5naCJHC8ucJylJ3w9/CVBaskdQ8WqBf8MmQrd1kRvp/a4TS8HJ+bxzR7ZJYQ==", "dev": true}, "node_modules/@types/sockjs": {"version": "0.3.33", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/ws": {"version": "8.5.3", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@voxeet/voxeet-web-sdk": {"version": "3.11.0-beta.1", "resolved": "https://registry.npmjs.org/@voxeet/voxeet-web-sdk/-/voxeet-web-sdk-3.11.0-beta.1.tgz", "integrity": "sha512-xyGvZn+STbrQ7b1MjiWHeFNaMrQ9sPlu3YeFmEmQxiG2JxLgP9mFRva//z6AERm66Y4swi7YW5XJ5rHpdWSNuQ==", "dependencies": {"axios": "^0.26.0", "bowser": "^2.8.1", "browserslist": "^4.5.2", "btoa": "^1.1.2", "debug": "^4.3.1", "events": "^1.1.0", "get-float-time-domain-data": "^0.1.0", "global": "^4.4.0", "jwt-decode": "3.1.2", "lodash.isequal": "4.5.0", "lodash.merge": "4.6.2", "moment": "^2.29.4", "sdp": "^2.3.0", "webrtc-adapter": "^6.3.2"}}, "node_modules/@voxeet/voxeet-web-sdk/node_modules/axios": {"version": "0.26.1", "resolved": "https://registry.npmjs.org/axios/-/axios-0.26.1.tgz", "integrity": "sha512-fPwcX4EvnSHuInCMItEhAGnaSEXRBjtzh9fOtsE6E1G6p7vl7edEeZe11QHf18+6+9gR5PbKV/sGKNaD8YaMeA==", "dependencies": {"follow-redirects": "^1.14.8"}}, "node_modules/@voxeet/voxeet-web-sdk/node_modules/events": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/events/-/events-1.1.1.tgz", "integrity": "sha512-kEcvvCBByWXGnZy6JUlgAp2gBIUjfCAV6P6TgT1/aaQKcmuAEC4OZTV1I4EWQLz2gxZw76atuVyvHhTxvi0Flw==", "engines": {"node": ">=0.4.x"}}, "node_modules/@webassemblyjs/ast": {"version": "1.11.1", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/helper-numbers": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1"}}, "node_modules/@webassemblyjs/floating-point-hex-parser": {"version": "1.11.1", "dev": true, "license": "MIT"}, "node_modules/@webassemblyjs/helper-api-error": {"version": "1.11.1", "dev": true, "license": "MIT"}, "node_modules/@webassemblyjs/helper-buffer": {"version": "1.11.1", "dev": true, "license": "MIT"}, "node_modules/@webassemblyjs/helper-numbers": {"version": "1.11.1", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/floating-point-hex-parser": "1.11.1", "@webassemblyjs/helper-api-error": "1.11.1", "@xtuc/long": "4.2.2"}}, "node_modules/@webassemblyjs/helper-wasm-bytecode": {"version": "1.11.1", "dev": true, "license": "MIT"}, "node_modules/@webassemblyjs/helper-wasm-section": {"version": "1.11.1", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/helper-buffer": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1", "@webassemblyjs/wasm-gen": "1.11.1"}}, "node_modules/@webassemblyjs/ieee754": {"version": "1.11.1", "dev": true, "license": "MIT", "dependencies": {"@xtuc/ieee754": "^1.2.0"}}, "node_modules/@webassemblyjs/leb128": {"version": "1.11.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@xtuc/long": "4.2.2"}}, "node_modules/@webassemblyjs/utf8": {"version": "1.11.1", "dev": true, "license": "MIT"}, "node_modules/@webassemblyjs/wasm-edit": {"version": "1.11.1", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/helper-buffer": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1", "@webassemblyjs/helper-wasm-section": "1.11.1", "@webassemblyjs/wasm-gen": "1.11.1", "@webassemblyjs/wasm-opt": "1.11.1", "@webassemblyjs/wasm-parser": "1.11.1", "@webassemblyjs/wast-printer": "1.11.1"}}, "node_modules/@webassemblyjs/wasm-gen": {"version": "1.11.1", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1", "@webassemblyjs/ieee754": "1.11.1", "@webassemblyjs/leb128": "1.11.1", "@webassemblyjs/utf8": "1.11.1"}}, "node_modules/@webassemblyjs/wasm-opt": {"version": "1.11.1", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/helper-buffer": "1.11.1", "@webassemblyjs/wasm-gen": "1.11.1", "@webassemblyjs/wasm-parser": "1.11.1"}}, "node_modules/@webassemblyjs/wasm-parser": {"version": "1.11.1", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/helper-api-error": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1", "@webassemblyjs/ieee754": "1.11.1", "@webassemblyjs/leb128": "1.11.1", "@webassemblyjs/utf8": "1.11.1"}}, "node_modules/@webassemblyjs/wast-printer": {"version": "1.11.1", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.11.1", "@xtuc/long": "4.2.2"}}, "node_modules/@webpack-cli/configtest": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-1.2.0.tgz", "integrity": "sha512-4FB8Tj6xyVkyqjj1OaTqCjXYULB9FMkqQ8yGrZjRDrYh0nOE+7Lhs45WioWQQMV+ceFlE368Ukhe6xdvJM9Egg==", "dev": true, "peerDependencies": {"webpack": "4.x.x || 5.x.x", "webpack-cli": "4.x.x"}}, "node_modules/@webpack-cli/info": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/@webpack-cli/info/-/info-1.5.0.tgz", "integrity": "sha512-e8tSXZpw2hPl2uMJY6fsMswaok5FdlGNRTktvFk2sD8RjH0hE2+XistawJx1vmKteh4NmGmNUrp+Tb2w+udPcQ==", "dev": true, "dependencies": {"envinfo": "^7.7.3"}, "peerDependencies": {"webpack-cli": "4.x.x"}}, "node_modules/@webpack-cli/serve": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-1.7.0.tgz", "integrity": "sha512-oxnCNGj88fL+xzV+dacXs44HcDwf1ovs3AuEzvP7mqXw7fQntqIhQ1BRmynh4qEKQSSSRSWVyXRjmTbZIX9V2Q==", "dev": true, "peerDependencies": {"webpack-cli": "4.x.x"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}}}, "node_modules/@xtuc/ieee754": {"version": "1.2.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@xtuc/long": {"version": "4.2.2", "dev": true, "license": "Apache-2.0"}, "node_modules/accepts": {"version": "1.3.7", "dev": true, "license": "MIT", "dependencies": {"mime-types": "~2.1.24", "negotiator": "0.6.2"}, "engines": {"node": ">= 0.6"}}, "node_modules/acorn": {"version": "8.8.0", "dev": true, "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-import-assertions": {"version": "1.7.6", "dev": true, "license": "MIT", "peerDependencies": {"acorn": "^8"}}, "node_modules/aggregate-error": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"clean-stack": "^2.0.0", "indent-string": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/aggregate-error/node_modules/indent-string": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/agora-rtc-sdk-ng": {"version": "4.21.0", "resolved": "https://registry.npmjs.org/agora-rtc-sdk-ng/-/agora-rtc-sdk-ng-4.21.0.tgz", "integrity": "sha512-EAZMdhbqIXl/PtFqEQn0O5Pmj3Tt+4oTXtd76MK1CozgbcDsc0TmFZK3qM25eaKqhzTz1wiVCwzBCWs3pF5OpQ==", "dependencies": {"@agora-js/media": "4.21.0", "@agora-js/report": "4.21.0", "@agora-js/shared": "4.21.0", "agora-rte-extension": "^1.2.4", "axios": "^1.6.8", "formdata-polyfill": "^4.0.7", "ua-parser-js": "^0.7.34", "webrtc-adapter": "8.2.0"}}, "node_modules/agora-rtc-sdk-ng/node_modules/sdp": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/sdp/-/sdp-3.2.0.tgz", "integrity": "sha512-d7wDPgDV3DDiqulJjKiV2865wKsJ34YI+NDREbm+FySq6WuKOikwyNQcm+doLAZ1O6ltdO0SeKle2xMpN3Brgw=="}, "node_modules/agora-rtc-sdk-ng/node_modules/webrtc-adapter": {"version": "8.2.0", "resolved": "https://registry.npmjs.org/webrtc-adapter/-/webrtc-adapter-8.2.0.tgz", "integrity": "sha512-umxCMgedPAVq4Pe/jl3xmelLXLn4XZWFEMR5Iipb5wJ+k1xMX0yC4ZY9CueZUU1MjapFxai1tFGE7R/kotH6Ww==", "dependencies": {"sdp": "^3.0.2"}, "engines": {"node": ">=6.0.0", "npm": ">=3.10.0"}}, "node_modules/agora-rte-extension": {"version": "1.2.4", "resolved": "https://registry.npmjs.org/agora-rte-extension/-/agora-rte-extension-1.2.4.tgz", "integrity": "sha512-0ovZz1lbe30QraG1cU+ji7EnQ8aUu+Hf3F+a8xPml3wPOyUQEK6CTdxV9kMecr9t+fIDrGeW7wgJTsM1DQE7Nw=="}, "node_modules/ajv": {"version": "6.12.6", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ajv-formats": {"version": "2.1.1", "dev": true, "license": "MIT", "dependencies": {"ajv": "^8.0.0"}, "peerDependencies": {"ajv": "^8.0.0"}, "peerDependenciesMeta": {"ajv": {"optional": true}}}, "node_modules/ajv-formats/node_modules/ajv": {"version": "8.10.0", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ajv-formats/node_modules/json-schema-traverse": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/ajv-keywords": {"version": "3.5.2", "dev": true, "license": "MIT", "peerDependencies": {"ajv": "^6.9.1"}}, "node_modules/ansi-html-community": {"version": "0.0.8", "dev": true, "engines": ["node >= 0.8.0"], "license": "Apache-2.0", "bin": {"ansi-html": "bin/ansi-html"}}, "node_modules/ansi-regex": {"version": "2.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/ansi-styles": {"version": "3.2.1", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/anymatch": {"version": "3.1.2", "dev": true, "license": "ISC", "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/arch": {"version": "2.2.0", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "optional": true}, "node_modules/archive-type": {"version": "4.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"file-type": "^4.2.0"}, "engines": {"node": ">=4"}}, "node_modules/archive-type/node_modules/file-type": {"version": "4.4.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/array-flatten": {"version": "2.1.2", "dev": true, "license": "MIT"}, "node_modules/array-union": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/arrify": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/async": {"version": "2.6.4", "dev": true, "license": "MIT", "dependencies": {"lodash": "^4.17.14"}}, "node_modules/asynckit": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q=="}, "node_modules/axios": {"version": "1.7.2", "resolved": "https://registry.npmjs.org/axios/-/axios-1.7.2.tgz", "integrity": "sha512-2A8QhOMrbomlDuiLeK9XibIBzuHeRcqqNOHp0Cyp5EoJ1IFDh+XZH3A6BkXtv0K4gFGCI0Y4BM7B1wOEi0Rmgw==", "dependencies": {"follow-redirects": "^1.15.6", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "node_modules/balanced-match": {"version": "1.0.2", "dev": true, "license": "MIT"}, "node_modules/base64-js": {"version": "1.5.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "optional": true}, "node_modules/batch": {"version": "0.6.1", "dev": true, "license": "MIT"}, "node_modules/big.js": {"version": "5.2.2", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/bin-build": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"decompress": "^4.0.0", "download": "^6.2.2", "execa": "^0.7.0", "p-map-series": "^1.0.0", "tempfile": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/bin-build/node_modules/cross-spawn": {"version": "5.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"lru-cache": "^4.0.1", "shebang-command": "^1.2.0", "which": "^1.2.9"}}, "node_modules/bin-build/node_modules/execa": {"version": "0.7.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"cross-spawn": "^5.0.1", "get-stream": "^3.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/bin-build/node_modules/get-stream": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/bin-build/node_modules/is-stream": {"version": "1.1.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/bin-build/node_modules/lru-cache": {"version": "4.1.5", "dev": true, "license": "ISC", "optional": true, "dependencies": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "node_modules/bin-build/node_modules/npm-run-path": {"version": "2.0.2", "dev": true, "license": "MIT", "optional": true, "dependencies": {"path-key": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/bin-build/node_modules/path-key": {"version": "2.0.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/bin-build/node_modules/shebang-command": {"version": "1.2.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"shebang-regex": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/bin-build/node_modules/shebang-regex": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/bin-build/node_modules/which": {"version": "1.3.1", "dev": true, "license": "ISC", "optional": true, "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "node_modules/bin-build/node_modules/yallist": {"version": "2.1.2", "dev": true, "license": "ISC", "optional": true}, "node_modules/bin-check": {"version": "4.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"execa": "^0.7.0", "executable": "^4.1.0"}, "engines": {"node": ">=4"}}, "node_modules/bin-check/node_modules/cross-spawn": {"version": "5.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"lru-cache": "^4.0.1", "shebang-command": "^1.2.0", "which": "^1.2.9"}}, "node_modules/bin-check/node_modules/execa": {"version": "0.7.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"cross-spawn": "^5.0.1", "get-stream": "^3.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/bin-check/node_modules/get-stream": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/bin-check/node_modules/is-stream": {"version": "1.1.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/bin-check/node_modules/lru-cache": {"version": "4.1.5", "dev": true, "license": "ISC", "optional": true, "dependencies": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "node_modules/bin-check/node_modules/npm-run-path": {"version": "2.0.2", "dev": true, "license": "MIT", "optional": true, "dependencies": {"path-key": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/bin-check/node_modules/path-key": {"version": "2.0.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/bin-check/node_modules/shebang-command": {"version": "1.2.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"shebang-regex": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/bin-check/node_modules/shebang-regex": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/bin-check/node_modules/which": {"version": "1.3.1", "dev": true, "license": "ISC", "optional": true, "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "node_modules/bin-check/node_modules/yallist": {"version": "2.1.2", "dev": true, "license": "ISC", "optional": true}, "node_modules/bin-version": {"version": "3.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"execa": "^1.0.0", "find-versions": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/bin-version-check": {"version": "4.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"bin-version": "^3.0.0", "semver": "^5.6.0", "semver-truncate": "^1.1.2"}, "engines": {"node": ">=6"}}, "node_modules/bin-version-check/node_modules/semver": {"version": "5.7.1", "dev": true, "license": "ISC", "optional": true, "bin": {"semver": "bin/semver"}}, "node_modules/bin-version/node_modules/cross-spawn": {"version": "6.0.5", "dev": true, "license": "MIT", "optional": true, "dependencies": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}, "engines": {"node": ">=4.8"}}, "node_modules/bin-version/node_modules/execa": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"cross-spawn": "^6.0.0", "get-stream": "^4.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "engines": {"node": ">=6"}}, "node_modules/bin-version/node_modules/get-stream": {"version": "4.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/bin-version/node_modules/is-stream": {"version": "1.1.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/bin-version/node_modules/npm-run-path": {"version": "2.0.2", "dev": true, "license": "MIT", "optional": true, "dependencies": {"path-key": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/bin-version/node_modules/path-key": {"version": "2.0.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/bin-version/node_modules/semver": {"version": "5.7.1", "dev": true, "license": "ISC", "optional": true, "bin": {"semver": "bin/semver"}}, "node_modules/bin-version/node_modules/shebang-command": {"version": "1.2.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"shebang-regex": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/bin-version/node_modules/shebang-regex": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/bin-version/node_modules/which": {"version": "1.3.1", "dev": true, "license": "ISC", "optional": true, "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "node_modules/bin-wrapper": {"version": "4.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"bin-check": "^4.1.0", "bin-version-check": "^4.0.0", "download": "^7.1.0", "import-lazy": "^3.1.0", "os-filter-obj": "^2.0.0", "pify": "^4.0.1"}, "engines": {"node": ">=6"}}, "node_modules/bin-wrapper/node_modules/download": {"version": "7.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"archive-type": "^4.0.0", "caw": "^2.0.1", "content-disposition": "^0.5.2", "decompress": "^4.2.0", "ext-name": "^5.0.0", "file-type": "^8.1.0", "filenamify": "^2.0.0", "get-stream": "^3.0.0", "got": "^8.3.1", "make-dir": "^1.2.0", "p-event": "^2.1.0", "pify": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/bin-wrapper/node_modules/download/node_modules/pify": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/bin-wrapper/node_modules/file-type": {"version": "8.1.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=6"}}, "node_modules/bin-wrapper/node_modules/get-stream": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/bin-wrapper/node_modules/got": {"version": "8.3.2", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@sindresorhus/is": "^0.7.0", "cacheable-request": "^2.1.1", "decompress-response": "^3.3.0", "duplexer3": "^0.1.4", "get-stream": "^3.0.0", "into-stream": "^3.1.0", "is-retry-allowed": "^1.1.0", "isurl": "^1.0.0-alpha5", "lowercase-keys": "^1.0.0", "mimic-response": "^1.0.0", "p-cancelable": "^0.4.0", "p-timeout": "^2.0.1", "pify": "^3.0.0", "safe-buffer": "^5.1.1", "timed-out": "^4.0.1", "url-parse-lax": "^3.0.0", "url-to-options": "^1.0.1"}, "engines": {"node": ">=4"}}, "node_modules/bin-wrapper/node_modules/got/node_modules/pify": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/bin-wrapper/node_modules/make-dir": {"version": "1.3.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"pify": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/bin-wrapper/node_modules/make-dir/node_modules/pify": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/bin-wrapper/node_modules/p-cancelable": {"version": "0.4.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/bin-wrapper/node_modules/p-event": {"version": "2.3.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"p-timeout": "^2.0.1"}, "engines": {"node": ">=6"}}, "node_modules/bin-wrapper/node_modules/p-timeout": {"version": "2.0.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"p-finally": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/bin-wrapper/node_modules/prepend-http": {"version": "2.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/bin-wrapper/node_modules/url-parse-lax": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"prepend-http": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/binary-extensions": {"version": "2.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/bl": {"version": "1.2.3", "dev": true, "license": "MIT", "optional": true, "dependencies": {"readable-stream": "^2.3.5", "safe-buffer": "^5.1.1"}}, "node_modules/bl/node_modules/readable-stream": {"version": "2.3.7", "dev": true, "license": "MIT", "optional": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/bl/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "license": "MIT", "optional": true}, "node_modules/bl/node_modules/string_decoder": {"version": "1.1.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/body-parser": {"version": "1.19.0", "dev": true, "license": "MIT", "dependencies": {"bytes": "3.1.0", "content-type": "~1.0.4", "debug": "2.6.9", "depd": "~1.1.2", "http-errors": "1.7.2", "iconv-lite": "0.4.24", "on-finished": "~2.3.0", "qs": "6.7.0", "raw-body": "2.4.0", "type-is": "~1.6.17"}, "engines": {"node": ">= 0.8"}}, "node_modules/body-parser/node_modules/bytes": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/body-parser/node_modules/debug": {"version": "2.6.9", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/body-parser/node_modules/ms": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/bonjour": {"version": "3.5.0", "dev": true, "license": "MIT", "dependencies": {"array-flatten": "^2.1.0", "deep-equal": "^1.0.1", "dns-equal": "^1.0.0", "dns-txt": "^2.0.2", "multicast-dns": "^6.0.1", "multicast-dns-service-types": "^1.1.0"}}, "node_modules/boolbase": {"version": "1.0.0", "dev": true, "license": "ISC"}, "node_modules/bootstrap": {"version": "5.3.0", "resolved": "https://registry.npmjs.org/bootstrap/-/bootstrap-5.3.0.tgz", "integrity": "sha512-UnBV3E3v4STVNQdms6jSGO2CvOkjUMdDAVR2V5N4uCMdaIkaQjbcEAMqRimDHIs4uqBYzDAKCQwCB+97tJgHQw==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/twbs"}, {"type": "opencollective", "url": "https://opencollective.com/bootstrap"}], "peerDependencies": {"@popperjs/core": "^2.11.7"}}, "node_modules/bowser": {"version": "2.11.0", "resolved": "https://registry.npmjs.org/bowser/-/bowser-2.11.0.tgz", "integrity": "sha512-AlcaJBi/pqqJBIQ8U9Mcpc9i8Aqxn88Skv5d+xBX006BY5u8N3mGLHa5Lgppa7L/HfwgwLgZ6NYs+Ag6uUmJRA=="}, "node_modules/brace-expansion": {"version": "1.1.11", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "3.0.2", "dev": true, "license": "MIT", "dependencies": {"fill-range": "^7.0.1"}, "engines": {"node": ">=8"}}, "node_modules/browserslist": {"version": "4.16.8", "license": "MIT", "dependencies": {"caniuse-lite": "^1.0.30001251", "colorette": "^1.3.0", "electron-to-chromium": "^1.3.811", "escalade": "^3.1.1", "node-releases": "^1.1.75"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/browserslist"}}, "node_modules/btoa": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/btoa/-/btoa-1.2.1.tgz", "integrity": "sha512-SB4/MIGlsiVkMcHmT+pSmIPoNDoHg+7cMzmt3Uxt628MTz2487DKSqK/fuhFBrkuqrYv5UCEnACpF4dTFNKc/g==", "bin": {"btoa": "bin/btoa.js"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/buffer": {"version": "5.7.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "optional": true, "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "node_modules/buffer-alloc": {"version": "1.2.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"buffer-alloc-unsafe": "^1.1.0", "buffer-fill": "^1.0.0"}}, "node_modules/buffer-alloc-unsafe": {"version": "1.1.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/buffer-crc32": {"version": "0.2.13", "dev": true, "license": "MIT", "optional": true, "engines": {"node": "*"}}, "node_modules/buffer-fill": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/buffer-from": {"version": "1.1.2", "dev": true, "license": "MIT"}, "node_modules/buffer-indexof": {"version": "1.1.1", "dev": true, "license": "MIT"}, "node_modules/bytes": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/cacheable-request": {"version": "2.1.4", "dev": true, "license": "MIT", "optional": true, "dependencies": {"clone-response": "1.0.2", "get-stream": "3.0.0", "http-cache-semantics": "3.8.1", "keyv": "3.0.0", "lowercase-keys": "1.0.0", "normalize-url": "2.0.1", "responselike": "1.0.2"}}, "node_modules/cacheable-request/node_modules/get-stream": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/cacheable-request/node_modules/lowercase-keys": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/call-bind": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/camel-case": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"pascal-case": "^3.1.2", "tslib": "^2.0.3"}}, "node_modules/camelcase": {"version": "6.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/camelcase-keys": {"version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"camelcase": "^6.2.0", "map-obj": "^4.1.0", "quick-lru": "^5.1.1", "type-fest": "^1.2.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/caniuse-lite": {"version": "1.0.30001252", "license": "CC-BY-4.0", "funding": {"type": "opencollective", "url": "https://opencollective.com/browserslist"}}, "node_modules/caw": {"version": "2.0.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"get-proxy": "^2.0.0", "isurl": "^1.0.0-alpha5", "tunnel-agent": "^0.6.0", "url-to-options": "^1.0.1"}, "engines": {"node": ">=4"}}, "node_modules/chalk": {"version": "2.4.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/chokidar": {"version": "3.5.3", "dev": true, "funding": [{"type": "individual", "url": "https://paulmillr.com/funding/"}], "license": "MIT", "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "engines": {"node": ">= 8.10.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/chrome-trace-event": {"version": "1.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=6.0"}}, "node_modules/clean-css": {"version": "4.2.3", "dev": true, "license": "MIT", "dependencies": {"source-map": "~0.6.0"}, "engines": {"node": ">= 4.0"}}, "node_modules/clean-stack": {"version": "2.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/clone-deep": {"version": "4.0.1", "dev": true, "license": "MIT", "dependencies": {"is-plain-object": "^2.0.4", "kind-of": "^6.0.2", "shallow-clone": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/clone-response": {"version": "1.0.2", "dev": true, "license": "MIT", "optional": true, "dependencies": {"mimic-response": "^1.0.0"}}, "node_modules/color-convert": {"version": "1.9.3", "dev": true, "license": "MIT", "dependencies": {"color-name": "1.1.3"}}, "node_modules/color-name": {"version": "1.1.3", "dev": true, "license": "MIT"}, "node_modules/colorette": {"version": "1.3.0", "license": "MIT"}, "node_modules/combined-stream": {"version": "1.0.8", "resolved": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz", "integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/commander": {"version": "4.1.1", "dev": true, "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/compressible": {"version": "2.0.18", "dev": true, "license": "MIT", "dependencies": {"mime-db": ">= 1.43.0 < 2"}, "engines": {"node": ">= 0.6"}}, "node_modules/compression": {"version": "1.7.4", "dev": true, "license": "MIT", "dependencies": {"accepts": "~1.3.5", "bytes": "3.0.0", "compressible": "~2.0.16", "debug": "2.6.9", "on-headers": "~1.0.2", "safe-buffer": "5.1.2", "vary": "~1.1.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/compression/node_modules/debug": {"version": "2.6.9", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/compression/node_modules/ms": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/compression/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "license": "MIT"}, "node_modules/concat-map": {"version": "0.0.1", "dev": true, "license": "MIT"}, "node_modules/config-chain": {"version": "1.1.13", "dev": true, "license": "MIT", "optional": true, "dependencies": {"ini": "^1.3.4", "proto-list": "~1.2.1"}}, "node_modules/connect-history-api-fallback": {"version": "1.6.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/content-disposition": {"version": "0.5.3", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "5.1.2"}, "engines": {"node": ">= 0.6"}}, "node_modules/content-disposition/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "license": "MIT"}, "node_modules/content-type": {"version": "1.0.4", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie": {"version": "0.4.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie-signature": {"version": "1.0.6", "dev": true, "license": "MIT"}, "node_modules/core-util-is": {"version": "1.0.3", "dev": true, "license": "MIT"}, "node_modules/cross-env": {"version": "7.0.3", "resolved": "https://registry.npmjs.org/cross-env/-/cross-env-7.0.3.tgz", "integrity": "sha512-+/HKd6EgcQCJGh2PSjZuUitQBQynKor4wrFbRg4DtAgS1aWO+gU52xpH7M9ScGgXSYmAVS9bIJ8EzuaGw0oNAw==", "dependencies": {"cross-spawn": "^7.0.1"}, "bin": {"cross-env": "src/bin/cross-env.js", "cross-env-shell": "src/bin/cross-env-shell.js"}, "engines": {"node": ">=10.14", "npm": ">=6", "yarn": ">=1"}}, "node_modules/cross-spawn": {"version": "7.0.3", "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/crypto-random-string": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/css-loader": {"version": "6.2.0", "dev": true, "license": "MIT", "dependencies": {"icss-utils": "^5.1.0", "postcss": "^8.2.15", "postcss-modules-extract-imports": "^3.0.0", "postcss-modules-local-by-default": "^4.0.0", "postcss-modules-scope": "^3.0.0", "postcss-modules-values": "^4.0.0", "postcss-value-parser": "^4.1.0", "semver": "^7.3.5"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.0.0"}}, "node_modules/css-select": {"version": "4.1.3", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0", "css-what": "^5.0.0", "domhandler": "^4.2.0", "domutils": "^2.6.0", "nth-check": "^2.0.0"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "node_modules/css-tree": {"version": "1.1.3", "dev": true, "license": "MIT", "optional": true, "dependencies": {"mdn-data": "2.0.14", "source-map": "^0.6.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/css-what": {"version": "5.0.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">= 6"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "node_modules/cssesc": {"version": "3.0.0", "dev": true, "license": "MIT", "bin": {"cssesc": "bin/cssesc"}, "engines": {"node": ">=4"}}, "node_modules/csso": {"version": "4.2.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"css-tree": "^1.1.2"}, "engines": {"node": ">=8.0.0"}}, "node_modules/cwebp-bin": {"version": "7.0.1", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "dependencies": {"bin-build": "^3.0.0", "bin-wrapper": "^4.0.1"}, "bin": {"cwebp": "cli.js"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/imagemin/cwebp-bin?sponsor=1"}}, "node_modules/debug": {"version": "4.3.2", "license": "MIT", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/decamelize": {"version": "5.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/decamelize-keys": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"decamelize": "^1.1.0", "map-obj": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/decamelize-keys/node_modules/decamelize": {"version": "1.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/decamelize-keys/node_modules/map-obj": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/decode-uri-component": {"version": "0.2.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10"}}, "node_modules/decompress": {"version": "4.2.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"decompress-tar": "^4.0.0", "decompress-tarbz2": "^4.0.0", "decompress-targz": "^4.0.0", "decompress-unzip": "^4.0.1", "graceful-fs": "^4.1.10", "make-dir": "^1.0.0", "pify": "^2.3.0", "strip-dirs": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/decompress-response": {"version": "3.3.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"mimic-response": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/decompress-tar": {"version": "4.1.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"file-type": "^5.2.0", "is-stream": "^1.1.0", "tar-stream": "^1.5.2"}, "engines": {"node": ">=4"}}, "node_modules/decompress-tar/node_modules/file-type": {"version": "5.2.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/decompress-tar/node_modules/is-stream": {"version": "1.1.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/decompress-tarbz2": {"version": "4.1.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"decompress-tar": "^4.1.0", "file-type": "^6.1.0", "is-stream": "^1.1.0", "seek-bzip": "^1.0.5", "unbzip2-stream": "^1.0.9"}, "engines": {"node": ">=4"}}, "node_modules/decompress-tarbz2/node_modules/file-type": {"version": "6.2.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/decompress-tarbz2/node_modules/is-stream": {"version": "1.1.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/decompress-targz": {"version": "4.1.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"decompress-tar": "^4.1.1", "file-type": "^5.2.0", "is-stream": "^1.1.0"}, "engines": {"node": ">=4"}}, "node_modules/decompress-targz/node_modules/file-type": {"version": "5.2.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/decompress-targz/node_modules/is-stream": {"version": "1.1.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/decompress-unzip": {"version": "4.0.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"file-type": "^3.8.0", "get-stream": "^2.2.0", "pify": "^2.3.0", "yauzl": "^2.4.2"}, "engines": {"node": ">=4"}}, "node_modules/decompress-unzip/node_modules/file-type": {"version": "3.9.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/decompress-unzip/node_modules/get-stream": {"version": "2.3.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"object-assign": "^4.0.1", "pinkie-promise": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/decompress-unzip/node_modules/pify": {"version": "2.3.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/decompress/node_modules/make-dir": {"version": "1.3.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"pify": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/decompress/node_modules/make-dir/node_modules/pify": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/decompress/node_modules/pify": {"version": "2.3.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/deep-equal": {"version": "1.1.1", "dev": true, "license": "MIT", "dependencies": {"is-arguments": "^1.0.4", "is-date-object": "^1.0.1", "is-regex": "^1.0.4", "object-is": "^1.0.1", "object-keys": "^1.1.1", "regexp.prototype.flags": "^1.2.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/default-gateway": {"version": "6.0.3", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"execa": "^5.0.0"}, "engines": {"node": ">= 10"}}, "node_modules/define-lazy-prop": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/define-properties": {"version": "1.1.3", "dev": true, "license": "MIT", "dependencies": {"object-keys": "^1.0.12"}, "engines": {"node": ">= 0.4"}}, "node_modules/del": {"version": "6.0.0", "dev": true, "license": "MIT", "dependencies": {"globby": "^11.0.1", "graceful-fs": "^4.2.4", "is-glob": "^4.0.1", "is-path-cwd": "^2.2.0", "is-path-inside": "^3.0.2", "p-map": "^4.0.0", "rimraf": "^3.0.2", "slash": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/delayed-stream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==", "engines": {"node": ">=0.4.0"}}, "node_modules/depd": {"version": "1.1.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/destroy": {"version": "1.0.4", "dev": true, "license": "MIT"}, "node_modules/detect-node": {"version": "2.1.0", "dev": true, "license": "MIT"}, "node_modules/dir-glob": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"path-type": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/dns-equal": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/dns-packet": {"version": "1.3.4", "dev": true, "license": "MIT", "dependencies": {"ip": "^1.1.0", "safe-buffer": "^5.0.1"}}, "node_modules/dns-txt": {"version": "2.0.2", "dev": true, "license": "MIT", "dependencies": {"buffer-indexof": "^1.0.0"}}, "node_modules/dom-converter": {"version": "0.2.0", "dev": true, "license": "MIT", "dependencies": {"utila": "~0.4"}}, "node_modules/dom-serializer": {"version": "1.3.2", "dev": true, "license": "MIT", "dependencies": {"domelementtype": "^2.0.1", "domhandler": "^4.2.0", "entities": "^2.0.0"}, "funding": {"url": "https://github.com/cheeriojs/dom-serializer?sponsor=1"}}, "node_modules/dom-walk": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/dom-walk/-/dom-walk-0.1.2.tgz", "integrity": "sha512-6QvTW9mrGeIegrFXdtQi9pk7O/nSK6lSdXW2eqUspN5LWD7UTji2Fqw5V2YLjBpHEoU9Xl/eUWNpDeZvoyOv2w=="}, "node_modules/domelementtype": {"version": "2.2.0", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/fb55"}], "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/domhandler": {"version": "4.2.2", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"domelementtype": "^2.2.0"}, "engines": {"node": ">= 4"}, "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}}, "node_modules/domutils": {"version": "2.8.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"dom-serializer": "^1.0.1", "domelementtype": "^2.2.0", "domhandler": "^4.2.0"}, "funding": {"url": "https://github.com/fb55/domutils?sponsor=1"}}, "node_modules/dot-case": {"version": "3.0.4", "dev": true, "license": "MIT", "dependencies": {"no-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/dotenv": {"version": "8.6.0", "resolved": "https://registry.npmjs.org/dotenv/-/dotenv-8.6.0.tgz", "integrity": "sha512-IrPdXQsk2BbzvCBGBOTmmSH5SodmqZNt4ERAZDmW4CT+tL8VtvinqywuANaFu4bOMWki16nqf0e4oC0QIaDr/g==", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=10"}}, "node_modules/dotenv-defaults": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/dotenv-defaults/-/dotenv-defaults-2.0.2.tgz", "integrity": "sha512-iOIzovWfsUHU91L5i8bJce3NYK5JXeAwH50Jh6+ARUdLiiGlYWfGw6UkzsYqaXZH/hjE/eCd/PlfM/qqyK0AMg==", "dev": true, "license": "MIT", "dependencies": {"dotenv": "^8.2.0"}}, "node_modules/dotenv-webpack": {"version": "8.1.0", "resolved": "https://registry.npmjs.org/dotenv-webpack/-/dotenv-webpack-8.1.0.tgz", "integrity": "sha512-owK1JcsPkIobeqjVrk6h7jPED/W6ZpdFsMPR+5ursB7/SdgDyO+VzAU+szK8C8u3qUhtENyYnj8eyXMR5kkGag==", "dev": true, "license": "MIT", "dependencies": {"dotenv-defaults": "^2.0.2"}, "engines": {"node": ">=10"}, "peerDependencies": {"webpack": "^4 || ^5"}}, "node_modules/download": {"version": "6.2.5", "dev": true, "license": "MIT", "optional": true, "dependencies": {"caw": "^2.0.0", "content-disposition": "^0.5.2", "decompress": "^4.0.0", "ext-name": "^5.0.0", "file-type": "5.2.0", "filenamify": "^2.0.0", "get-stream": "^3.0.0", "got": "^7.0.0", "make-dir": "^1.0.0", "p-event": "^1.0.0", "pify": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/download/node_modules/file-type": {"version": "5.2.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/download/node_modules/get-stream": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/download/node_modules/make-dir": {"version": "1.3.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"pify": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/download/node_modules/pify": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/duplexer3": {"version": "0.1.5", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true}, "node_modules/ee-first": {"version": "1.1.1", "dev": true, "license": "MIT"}, "node_modules/electron-to-chromium": {"version": "1.3.828", "license": "ISC"}, "node_modules/emojis-list": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/encodeurl": {"version": "1.0.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/end-of-stream": {"version": "1.4.4", "dev": true, "license": "MIT", "optional": true, "dependencies": {"once": "^1.4.0"}}, "node_modules/enhanced-resolve": {"version": "5.8.2", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.4", "tapable": "^2.2.0"}, "engines": {"node": ">=10.13.0"}}, "node_modules/entities": {"version": "2.2.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/envinfo": {"version": "7.10.0", "resolved": "https://registry.npmjs.org/envinfo/-/envinfo-7.10.0.tgz", "integrity": "sha512-ZtUjZO6l5mwTHvc1L9+1q5p/R3wTopcfqMW8r5t8SJSKqeVI/LtajORwRFEKpEFuekjD0VBjwu1HMxL4UalIRw==", "dev": true, "bin": {"envinfo": "dist/cli.js"}, "engines": {"node": ">=4"}}, "node_modules/error-ex": {"version": "1.3.2", "dev": true, "license": "MIT", "dependencies": {"is-arrayish": "^0.2.1"}}, "node_modules/es-module-lexer": {"version": "0.7.1", "dev": true, "license": "MIT"}, "node_modules/escalade": {"version": "3.1.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-html": {"version": "1.0.3", "dev": true, "license": "MIT"}, "node_modules/escape-string-regexp": {"version": "1.0.5", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/eslint-scope": {"version": "5.1.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/esrecurse": {"version": "4.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/esrecurse/node_modules/estraverse": {"version": "5.2.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "4.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/etag": {"version": "1.8.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/eventemitter3": {"version": "4.0.7", "dev": true, "license": "MIT"}, "node_modules/events": {"version": "3.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.x"}}, "node_modules/exec-buffer": {"version": "3.2.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"execa": "^0.7.0", "p-finally": "^1.0.0", "pify": "^3.0.0", "rimraf": "^2.5.4", "tempfile": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/exec-buffer/node_modules/cross-spawn": {"version": "5.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"lru-cache": "^4.0.1", "shebang-command": "^1.2.0", "which": "^1.2.9"}}, "node_modules/exec-buffer/node_modules/execa": {"version": "0.7.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"cross-spawn": "^5.0.1", "get-stream": "^3.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/exec-buffer/node_modules/get-stream": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/exec-buffer/node_modules/is-stream": {"version": "1.1.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/exec-buffer/node_modules/lru-cache": {"version": "4.1.5", "dev": true, "license": "ISC", "optional": true, "dependencies": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "node_modules/exec-buffer/node_modules/npm-run-path": {"version": "2.0.2", "dev": true, "license": "MIT", "optional": true, "dependencies": {"path-key": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/exec-buffer/node_modules/path-key": {"version": "2.0.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/exec-buffer/node_modules/pify": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/exec-buffer/node_modules/rimraf": {"version": "2.7.1", "dev": true, "license": "ISC", "optional": true, "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}}, "node_modules/exec-buffer/node_modules/shebang-command": {"version": "1.2.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"shebang-regex": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/exec-buffer/node_modules/shebang-regex": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/exec-buffer/node_modules/which": {"version": "1.3.1", "dev": true, "license": "ISC", "optional": true, "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "node_modules/exec-buffer/node_modules/yallist": {"version": "2.1.2", "dev": true, "license": "ISC", "optional": true}, "node_modules/execa": {"version": "5.1.1", "dev": true, "license": "MIT", "dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/executable": {"version": "4.1.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"pify": "^2.2.0"}, "engines": {"node": ">=4"}}, "node_modules/executable/node_modules/pify": {"version": "2.3.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/express": {"version": "4.17.1", "dev": true, "license": "MIT", "dependencies": {"accepts": "~1.3.7", "array-flatten": "1.1.1", "body-parser": "1.19.0", "content-disposition": "0.5.3", "content-type": "~1.0.4", "cookie": "0.4.0", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "~1.1.2", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "~1.1.2", "fresh": "0.5.2", "merge-descriptors": "1.0.1", "methods": "~1.1.2", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "path-to-regexp": "0.1.7", "proxy-addr": "~2.0.5", "qs": "6.7.0", "range-parser": "~1.2.1", "safe-buffer": "5.1.2", "send": "0.17.1", "serve-static": "1.14.1", "setprototypeof": "1.1.1", "statuses": "~1.5.0", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/express/node_modules/array-flatten": {"version": "1.1.1", "dev": true, "license": "MIT"}, "node_modules/express/node_modules/debug": {"version": "2.6.9", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/express/node_modules/ms": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/express/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "license": "MIT"}, "node_modules/ext-list": {"version": "2.2.2", "dev": true, "license": "MIT", "optional": true, "dependencies": {"mime-db": "^1.28.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ext-name": {"version": "5.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"ext-list": "^2.0.0", "sort-keys-length": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/fast-deep-equal": {"version": "3.1.3", "dev": true, "license": "MIT"}, "node_modules/fast-glob": {"version": "3.2.7", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.4"}, "engines": {"node": ">=8"}}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "dev": true, "license": "MIT"}, "node_modules/fast-xml-parser": {"version": "3.21.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"strnum": "^1.0.4"}, "bin": {"xml2js": "cli.js"}, "funding": {"type": "paypal", "url": "https://paypal.me/naturalintelligence"}}, "node_modules/fastest-levenshtein": {"version": "1.0.12", "dev": true, "license": "MIT"}, "node_modules/fastq": {"version": "1.12.0", "dev": true, "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "node_modules/faye-websocket": {"version": "0.11.4", "dev": true, "license": "Apache-2.0", "dependencies": {"websocket-driver": ">=0.5.1"}, "engines": {"node": ">=0.8.0"}}, "node_modules/fd-slicer": {"version": "1.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"pend": "~1.2.0"}}, "node_modules/fetch-blob": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-3.2.0.tgz", "integrity": "sha512-7yAQpD2UMJzLi1Dqv7qFYnPbaPx7ZfFK6PiIxQ4PfkGPyNyl2Ugx+a/umUonmKqjhM4DnfbMvdX6otXq83soQQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "paypal", "url": "https://paypal.me/jimmywarting"}], "dependencies": {"node-domexception": "^1.0.0", "web-streams-polyfill": "^3.0.3"}, "engines": {"node": "^12.20 || >= 14.13"}}, "node_modules/file-type": {"version": "16.5.3", "dev": true, "license": "MIT", "dependencies": {"readable-web-to-node-stream": "^3.0.0", "strtok3": "^6.2.4", "token-types": "^4.1.1"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/file-type?sponsor=1"}}, "node_modules/filename-reserved-regex": {"version": "2.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/filenamify": {"version": "2.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"filename-reserved-regex": "^2.0.0", "strip-outer": "^1.0.0", "trim-repeated": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/fill-range": {"version": "7.0.1", "dev": true, "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/finalhandler": {"version": "1.1.2", "dev": true, "license": "MIT", "dependencies": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "statuses": "~1.5.0", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/finalhandler/node_modules/debug": {"version": "2.6.9", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/finalhandler/node_modules/ms": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/find-up": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/find-versions": {"version": "3.2.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"semver-regex": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/follow-redirects": {"version": "1.15.6", "resolved": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.6.tgz", "integrity": "sha512-wWN62YITEaOpSK584EZXJafH1AGpO8RVgElfkuXbTOrPX4fIfOyEpW/CsiNd8JdYrAoOvafRTOEnvsO++qCqFA==", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/form-data": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/form-data/-/form-data-4.0.0.tgz", "integrity": "sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/formdata-polyfill": {"version": "4.0.10", "resolved": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-4.0.10.tgz", "integrity": "sha512-buewHzMvYL29jdeQTVILecSaZKnt/RJWjoZCF5OW60Z67/GmSLBkOFM7qh1PI3zFNtJbaZL5eQu1vLfazOwj4g==", "dependencies": {"fetch-blob": "^3.1.2"}, "engines": {"node": ">=12.20.0"}}, "node_modules/forwarded": {"version": "0.2.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fresh": {"version": "0.5.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/from2": {"version": "2.3.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"inherits": "^2.0.1", "readable-stream": "^2.0.0"}}, "node_modules/from2/node_modules/readable-stream": {"version": "2.3.7", "dev": true, "license": "MIT", "optional": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/from2/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "license": "MIT", "optional": true}, "node_modules/from2/node_modules/string_decoder": {"version": "1.1.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/fs-constants": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/fs-monkey": {"version": "1.0.3", "dev": true, "license": "Unlicense"}, "node_modules/fs.realpath": {"version": "1.0.0", "dev": true, "license": "ISC"}, "node_modules/function-bind": {"version": "1.1.1", "dev": true, "license": "MIT"}, "node_modules/get-float-time-domain-data": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/get-float-time-domain-data/-/get-float-time-domain-data-0.1.0.tgz", "integrity": "sha512-6lWdVMny7vJ+xWxGUmVdSW7rd4L2byptiVkjH3QEBsxcHSa8UbC/HsZmixHK/ZQ1EYsOaQjaeRgR+NAsIerEJQ=="}, "node_modules/get-intrinsic": {"version": "1.1.1", "dev": true, "license": "MIT", "dependencies": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.1"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-proxy": {"version": "2.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"npm-conf": "^1.1.0"}, "engines": {"node": ">=4"}}, "node_modules/get-stdin": {"version": "9.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/get-stream": {"version": "6.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/gifsicle": {"version": "5.3.0", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "dependencies": {"bin-build": "^3.0.0", "bin-wrapper": "^4.0.0", "execa": "^5.0.0"}, "bin": {"gifsicle": "cli.js"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/imagemin/gisicle-bin?sponsor=1"}}, "node_modules/glob": {"version": "7.1.7", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "5.1.2", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/glob-to-regexp": {"version": "0.4.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/global": {"version": "4.4.0", "resolved": "https://registry.npmjs.org/global/-/global-4.4.0.tgz", "integrity": "sha512-wv/LAoHdRE3BeTGz53FAamhGlPLhlssK45usmGFThIi4XqnBmjKQ16u+RNbP7WvigRZDxUsM0J3gcQ5yicaL0w==", "dependencies": {"min-document": "^2.19.0", "process": "^0.11.10"}}, "node_modules/globby": {"version": "11.0.4", "dev": true, "license": "MIT", "dependencies": {"array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.1.1", "ignore": "^5.1.4", "merge2": "^1.3.0", "slash": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/got": {"version": "7.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"decompress-response": "^3.2.0", "duplexer3": "^0.1.4", "get-stream": "^3.0.0", "is-plain-obj": "^1.1.0", "is-retry-allowed": "^1.0.0", "is-stream": "^1.0.0", "isurl": "^1.0.0-alpha5", "lowercase-keys": "^1.0.0", "p-cancelable": "^0.3.0", "p-timeout": "^1.1.1", "safe-buffer": "^5.0.1", "timed-out": "^4.0.0", "url-parse-lax": "^1.0.0", "url-to-options": "^1.0.1"}, "engines": {"node": ">=4"}}, "node_modules/got/node_modules/get-stream": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/got/node_modules/is-stream": {"version": "1.1.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/graceful-fs": {"version": "4.2.8", "dev": true, "license": "ISC"}, "node_modules/handle-thing": {"version": "2.0.1", "dev": true, "license": "MIT"}, "node_modules/hard-rejection": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/has": {"version": "1.0.3", "dev": true, "license": "MIT", "dependencies": {"function-bind": "^1.1.1"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/has-flag": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/has-symbol-support-x": {"version": "1.4.2", "dev": true, "license": "MIT", "optional": true, "engines": {"node": "*"}}, "node_modules/has-symbols": {"version": "1.0.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-to-string-tag-x": {"version": "1.4.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"has-symbol-support-x": "^1.4.1"}, "engines": {"node": "*"}}, "node_modules/has-tostringtag": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"has-symbols": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/he": {"version": "1.2.0", "dev": true, "license": "MIT", "bin": {"he": "bin/he"}}, "node_modules/hosted-git-info": {"version": "4.0.2", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "engines": {"node": ">=10"}}, "node_modules/hpack.js": {"version": "2.1.6", "dev": true, "license": "MIT", "dependencies": {"inherits": "^2.0.1", "obuf": "^1.0.0", "readable-stream": "^2.0.1", "wbuf": "^1.1.0"}}, "node_modules/hpack.js/node_modules/readable-stream": {"version": "2.3.7", "dev": true, "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/hpack.js/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "license": "MIT"}, "node_modules/hpack.js/node_modules/string_decoder": {"version": "1.1.1", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/html-entities": {"version": "2.3.2", "dev": true, "license": "MIT"}, "node_modules/html-loader": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"html-minifier-terser": "^6.1.0", "parse5": "^7.0.0"}, "engines": {"node": ">= 14.15.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.0.0"}}, "node_modules/html-loader/node_modules/clean-css": {"version": "5.3.1", "dev": true, "license": "MIT", "dependencies": {"source-map": "~0.6.0"}, "engines": {"node": ">= 10.0"}}, "node_modules/html-loader/node_modules/commander": {"version": "8.3.0", "dev": true, "license": "MIT", "engines": {"node": ">= 12"}}, "node_modules/html-loader/node_modules/html-minifier-terser": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"camel-case": "^4.1.2", "clean-css": "^5.2.2", "commander": "^8.3.0", "he": "^1.2.0", "param-case": "^3.0.4", "relateurl": "^0.2.7", "terser": "^5.10.0"}, "bin": {"html-minifier-terser": "cli.js"}, "engines": {"node": ">=12"}}, "node_modules/html-loader/node_modules/terser": {"version": "5.14.2", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@jridgewell/source-map": "^0.3.2", "acorn": "^8.5.0", "commander": "^2.20.0", "source-map-support": "~0.5.20"}, "bin": {"terser": "bin/terser"}, "engines": {"node": ">=10"}}, "node_modules/html-loader/node_modules/terser/node_modules/commander": {"version": "2.20.3", "dev": true, "license": "MIT"}, "node_modules/html-minifier-terser": {"version": "5.1.1", "dev": true, "license": "MIT", "dependencies": {"camel-case": "^4.1.1", "clean-css": "^4.2.3", "commander": "^4.1.1", "he": "^1.2.0", "param-case": "^3.0.3", "relateurl": "^0.2.7", "terser": "^4.6.3"}, "bin": {"html-minifier-terser": "cli.js"}, "engines": {"node": ">=6"}}, "node_modules/html-webpack-plugin": {"version": "5.3.2", "dev": true, "license": "MIT", "dependencies": {"@types/html-minifier-terser": "^5.0.0", "html-minifier-terser": "^5.0.1", "lodash": "^4.17.21", "pretty-error": "^3.0.4", "tapable": "^2.0.0"}, "engines": {"node": ">=10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/html-webpack-plugin"}, "peerDependencies": {"webpack": "^5.20.0"}}, "node_modules/htmlparser2": {"version": "6.1.0", "dev": true, "funding": ["https://github.com/fb55/htmlparser2?sponsor=1", {"type": "github", "url": "https://github.com/sponsors/fb55"}], "license": "MIT", "dependencies": {"domelementtype": "^2.0.1", "domhandler": "^4.0.0", "domutils": "^2.5.2", "entities": "^2.0.0"}}, "node_modules/http-cache-semantics": {"version": "3.8.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "optional": true}, "node_modules/http-deceiver": {"version": "1.2.7", "dev": true, "license": "MIT"}, "node_modules/http-errors": {"version": "1.7.2", "dev": true, "license": "MIT", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.1", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/http-errors/node_modules/inherits": {"version": "2.0.3", "dev": true, "license": "ISC"}, "node_modules/http-parser-js": {"version": "0.5.3", "dev": true, "license": "MIT"}, "node_modules/http-proxy": {"version": "1.18.1", "dev": true, "license": "MIT", "dependencies": {"eventemitter3": "^4.0.0", "follow-redirects": "^1.0.0", "requires-port": "^1.0.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/http-proxy-middleware": {"version": "2.0.4", "dev": true, "license": "MIT", "dependencies": {"@types/http-proxy": "^1.17.8", "http-proxy": "^1.18.1", "is-glob": "^4.0.1", "is-plain-obj": "^3.0.0", "micromatch": "^4.0.2"}, "engines": {"node": ">=12.0.0"}, "peerDependencies": {"@types/express": "^4.17.13"}, "peerDependenciesMeta": {"@types/express": {"optional": true}}}, "node_modules/http-proxy-middleware/node_modules/is-plain-obj": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/human-signals": {"version": "2.1.0", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=10.17.0"}}, "node_modules/iconv-lite": {"version": "0.4.24", "dev": true, "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/icss-utils": {"version": "5.1.0", "dev": true, "license": "ISC", "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/ieee754": {"version": "1.2.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ignore": {"version": "5.1.8", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/image-webpack-loader": {"version": "8.1.0", "dev": true, "license": "MIT", "dependencies": {"imagemin": "^7.0.1", "loader-utils": "^2.0.0", "object-assign": "^4.1.1", "schema-utils": "^2.7.1"}, "optionalDependencies": {"imagemin-gifsicle": "^7.0.0", "imagemin-mozjpeg": "^9.0.0", "imagemin-optipng": "^8.0.0", "imagemin-pngquant": "^9.0.2", "imagemin-svgo": "^9.0.0", "imagemin-webp": "^7.0.0"}}, "node_modules/image-webpack-loader/node_modules/schema-utils": {"version": "2.7.1", "dev": true, "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.5", "ajv": "^6.12.4", "ajv-keywords": "^3.5.2"}, "engines": {"node": ">= 8.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/imagemin": {"version": "7.0.1", "dev": true, "license": "MIT", "dependencies": {"file-type": "^12.0.0", "globby": "^10.0.0", "graceful-fs": "^4.2.2", "junk": "^3.1.0", "make-dir": "^3.0.0", "p-pipe": "^3.0.0", "replace-ext": "^1.0.0"}, "engines": {"node": ">=8"}}, "node_modules/imagemin-gifsicle": {"version": "7.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"execa": "^1.0.0", "gifsicle": "^5.0.0", "is-gif": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/imagemin/imagemin-gifsicle?sponsor=1"}}, "node_modules/imagemin-gifsicle/node_modules/cross-spawn": {"version": "6.0.5", "dev": true, "license": "MIT", "optional": true, "dependencies": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}, "engines": {"node": ">=4.8"}}, "node_modules/imagemin-gifsicle/node_modules/execa": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"cross-spawn": "^6.0.0", "get-stream": "^4.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "engines": {"node": ">=6"}}, "node_modules/imagemin-gifsicle/node_modules/get-stream": {"version": "4.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/imagemin-gifsicle/node_modules/is-stream": {"version": "1.1.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/imagemin-gifsicle/node_modules/npm-run-path": {"version": "2.0.2", "dev": true, "license": "MIT", "optional": true, "dependencies": {"path-key": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/imagemin-gifsicle/node_modules/path-key": {"version": "2.0.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/imagemin-gifsicle/node_modules/semver": {"version": "5.7.1", "dev": true, "license": "ISC", "optional": true, "bin": {"semver": "bin/semver"}}, "node_modules/imagemin-gifsicle/node_modules/shebang-command": {"version": "1.2.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"shebang-regex": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/imagemin-gifsicle/node_modules/shebang-regex": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/imagemin-gifsicle/node_modules/which": {"version": "1.3.1", "dev": true, "license": "ISC", "optional": true, "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "node_modules/imagemin-mozjpeg": {"version": "9.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"execa": "^4.0.0", "is-jpg": "^2.0.0", "mozjpeg": "^7.0.0"}, "engines": {"node": ">=10"}}, "node_modules/imagemin-mozjpeg/node_modules/execa": {"version": "4.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"cross-spawn": "^7.0.0", "get-stream": "^5.0.0", "human-signals": "^1.1.1", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "onetime": "^5.1.0", "signal-exit": "^3.0.2", "strip-final-newline": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/imagemin-mozjpeg/node_modules/get-stream": {"version": "5.2.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/imagemin-mozjpeg/node_modules/human-signals": {"version": "1.1.1", "dev": true, "license": "Apache-2.0", "optional": true, "engines": {"node": ">=8.12.0"}}, "node_modules/imagemin-optipng": {"version": "8.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"exec-buffer": "^3.0.0", "is-png": "^2.0.0", "optipng-bin": "^7.0.0"}, "engines": {"node": ">=10"}}, "node_modules/imagemin-pngquant": {"version": "9.0.2", "dev": true, "license": "MIT", "optional": true, "dependencies": {"execa": "^4.0.0", "is-png": "^2.0.0", "is-stream": "^2.0.0", "ow": "^0.17.0", "pngquant-bin": "^6.0.0"}, "engines": {"node": ">=10"}}, "node_modules/imagemin-pngquant/node_modules/execa": {"version": "4.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"cross-spawn": "^7.0.0", "get-stream": "^5.0.0", "human-signals": "^1.1.1", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "onetime": "^5.1.0", "signal-exit": "^3.0.2", "strip-final-newline": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/imagemin-pngquant/node_modules/get-stream": {"version": "5.2.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/imagemin-pngquant/node_modules/human-signals": {"version": "1.1.1", "dev": true, "license": "Apache-2.0", "optional": true, "engines": {"node": ">=8.12.0"}}, "node_modules/imagemin-svgo": {"version": "9.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"is-svg": "^4.2.1", "svgo": "^2.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/imagemin-svgo?sponsor=1"}}, "node_modules/imagemin-webp": {"version": "7.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"cwebp-bin": "^7.0.1", "exec-buffer": "^3.2.0", "is-cwebp-readable": "^3.0.0"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}}, "node_modules/imagemin/node_modules/file-type": {"version": "12.4.2", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/imagemin/node_modules/globby": {"version": "10.0.2", "dev": true, "license": "MIT", "dependencies": {"@types/glob": "^7.1.1", "array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.0.3", "glob": "^7.1.3", "ignore": "^5.1.1", "merge2": "^1.2.3", "slash": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/import-lazy": {"version": "3.1.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=6"}}, "node_modules/import-local": {"version": "3.0.2", "dev": true, "license": "MIT", "dependencies": {"pkg-dir": "^4.2.0", "resolve-cwd": "^3.0.0"}, "bin": {"import-local-fixture": "fixtures/cli.js"}, "engines": {"node": ">=8"}}, "node_modules/indent-string": {"version": "5.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/inflight": {"version": "1.0.6", "dev": true, "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "dev": true, "license": "ISC"}, "node_modules/ini": {"version": "1.3.8", "dev": true, "license": "ISC", "optional": true}, "node_modules/interpret": {"version": "2.2.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/into-stream": {"version": "3.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"from2": "^2.1.1", "p-is-promise": "^1.1.0"}, "engines": {"node": ">=4"}}, "node_modules/ip": {"version": "1.1.5", "dev": true, "license": "MIT"}, "node_modules/ipaddr.js": {"version": "1.9.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/is-arguments": {"version": "1.1.1", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-arrayish": {"version": "0.2.1", "dev": true, "license": "MIT"}, "node_modules/is-binary-path": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"binary-extensions": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/is-core-module": {"version": "2.6.0", "dev": true, "license": "MIT", "dependencies": {"has": "^1.0.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-cwebp-readable": {"version": "3.0.0", "dev": true, "license": "ISC", "optional": true, "dependencies": {"file-type": "^10.5.0"}}, "node_modules/is-cwebp-readable/node_modules/file-type": {"version": "10.11.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=6"}}, "node_modules/is-date-object": {"version": "1.0.5", "dev": true, "license": "MIT", "dependencies": {"has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-docker": {"version": "2.2.1", "dev": true, "license": "MIT", "bin": {"is-docker": "cli.js"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-extglob": {"version": "2.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-gif": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"file-type": "^10.4.0"}, "engines": {"node": ">=6"}}, "node_modules/is-gif/node_modules/file-type": {"version": "10.11.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=6"}}, "node_modules/is-glob": {"version": "4.0.1", "dev": true, "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-jpg": {"version": "2.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=6"}}, "node_modules/is-natural-number": {"version": "4.0.1", "dev": true, "license": "MIT", "optional": true}, "node_modules/is-number": {"version": "7.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-object": {"version": "1.0.2", "dev": true, "license": "MIT", "optional": true, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-path-cwd": {"version": "2.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/is-path-inside": {"version": "3.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-plain-obj": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-plain-object": {"version": "2.0.4", "dev": true, "license": "MIT", "dependencies": {"isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-png": {"version": "2.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=8"}}, "node_modules/is-regex": {"version": "1.1.4", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-retry-allowed": {"version": "1.2.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/is-stream": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-svg": {"version": "4.3.2", "dev": true, "license": "MIT", "optional": true, "dependencies": {"fast-xml-parser": "^3.19.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-wsl": {"version": "2.2.0", "dev": true, "license": "MIT", "dependencies": {"is-docker": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/isarray": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/isexe": {"version": "2.0.0", "license": "ISC"}, "node_modules/isobject": {"version": "3.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/isurl": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"has-to-string-tag-x": "^1.2.0", "is-object": "^1.0.1"}, "engines": {"node": ">= 4"}}, "node_modules/jest-worker": {"version": "27.1.0", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "engines": {"node": ">= 10.13.0"}}, "node_modules/jest-worker/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/jest-worker/node_modules/supports-color": {"version": "8.1.1", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/js-tokens": {"version": "4.0.0", "dev": true, "license": "MIT"}, "node_modules/json-buffer": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/json-parse-better-errors": {"version": "1.0.2", "dev": true, "license": "MIT"}, "node_modules/json-parse-even-better-errors": {"version": "2.3.1", "dev": true, "license": "MIT"}, "node_modules/json-schema-traverse": {"version": "0.4.1", "dev": true, "license": "MIT"}, "node_modules/json5": {"version": "2.2.1", "dev": true, "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/junk": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/jwt-decode": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/jwt-decode/-/jwt-decode-3.1.2.tgz", "integrity": "sha512-UfpWE/VZn0iP50d8cz9NrZLM9lSWhcJ+0Gt/nm4by88UL+J1SiKN8/5dkjMmbEzwL2CAe+67GsegCbIKtbp75A=="}, "node_modules/keyv": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"json-buffer": "3.0.0"}}, "node_modules/kind-of": {"version": "6.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/lines-and-columns": {"version": "1.1.6", "dev": true, "license": "MIT"}, "node_modules/loader-runner": {"version": "4.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=6.11.5"}}, "node_modules/loader-utils": {"version": "2.0.2", "dev": true, "license": "MIT", "dependencies": {"big.js": "^5.2.2", "emojis-list": "^3.0.0", "json5": "^2.1.2"}, "engines": {"node": ">=8.9.0"}}, "node_modules/locate-path": {"version": "6.0.0", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lodash": {"version": "4.17.21", "dev": true, "license": "MIT"}, "node_modules/lodash.isequal": {"version": "4.5.0", "resolved": "https://registry.npmjs.org/lodash.isequal/-/lodash.isequal-4.5.0.tgz", "integrity": "sha512-pDo3lu8Jhfjqls6GkMgpahsF9kCyayhgykjyLMNFTKWrpVdAQtYyB4muAMWozBB4ig/dtWAmsMxLEI8wuz+DYQ=="}, "node_modules/lodash.merge": {"version": "4.6.2", "resolved": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz", "integrity": "sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ=="}, "node_modules/lower-case": {"version": "2.0.2", "dev": true, "license": "MIT", "dependencies": {"tslib": "^2.0.3"}}, "node_modules/lowercase-keys": {"version": "1.0.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/make-dir": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"semver": "^6.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/make-dir/node_modules/semver": {"version": "6.3.0", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/map-obj": {"version": "4.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/mdn-data": {"version": "2.0.14", "dev": true, "license": "CC0-1.0", "optional": true}, "node_modules/media-typer": {"version": "0.3.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/memfs": {"version": "3.4.1", "dev": true, "license": "Unlicense", "dependencies": {"fs-monkey": "1.0.3"}, "engines": {"node": ">= 4.0.0"}}, "node_modules/meow": {"version": "10.1.1", "dev": true, "license": "MIT", "dependencies": {"@types/minimist": "^1.2.2", "camelcase-keys": "^7.0.0", "decamelize": "^5.0.0", "decamelize-keys": "^1.1.0", "hard-rejection": "^2.1.0", "minimist-options": "4.1.0", "normalize-package-data": "^3.0.2", "read-pkg-up": "^8.0.0", "redent": "^4.0.0", "trim-newlines": "^4.0.2", "type-fest": "^1.2.2", "yargs-parser": "^20.2.9"}, "engines": {"node": ">=12.17"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/merge-descriptors": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/merge-stream": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/merge2": {"version": "1.4.1", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/methods": {"version": "1.1.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/micromatch": {"version": "4.0.4", "dev": true, "license": "MIT", "dependencies": {"braces": "^3.0.1", "picomatch": "^2.2.3"}, "engines": {"node": ">=8.6"}}, "node_modules/mime": {"version": "1.6.0", "dev": true, "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/mime-db": {"version": "1.49.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.32", "license": "MIT", "dependencies": {"mime-db": "1.49.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mimic-fn": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/mimic-response": {"version": "1.0.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/min-document": {"version": "2.19.0", "resolved": "https://registry.npmjs.org/min-document/-/min-document-2.19.0.tgz", "integrity": "sha512-9Wy1B3m3f66bPPmU5hdA4DR4PB2OfDU/+GS3yAB7IQozE3tqXaVv2zOjgla7MEGSRv95+ILmOuvhLkOK6wJtCQ==", "dependencies": {"dom-walk": "^0.1.0"}}, "node_modules/min-indent": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/mini-css-extract-plugin": {"version": "2.2.2", "dev": true, "license": "MIT", "dependencies": {"schema-utils": "^3.1.0"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.0.0"}}, "node_modules/minimalistic-assert": {"version": "1.0.1", "dev": true, "license": "ISC"}, "node_modules/minimatch": {"version": "3.0.4", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.6", "dev": true, "license": "MIT"}, "node_modules/minimist-options": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"arrify": "^1.0.1", "is-plain-obj": "^1.1.0", "kind-of": "^6.0.3"}, "engines": {"node": ">= 6"}}, "node_modules/mkdirp": {"version": "0.5.5", "dev": true, "license": "MIT", "dependencies": {"minimist": "^1.2.5"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/moment": {"version": "2.29.4", "resolved": "https://registry.npmjs.org/moment/-/moment-2.29.4.tgz", "integrity": "sha512-5LC9SOxjSc2HF6vO2CyuTDNivEdoz2IvyJJGj6X8DJ0eFyfszE0QiEd+iXmBvUP3WHxSjFH/vIsA0EN00cgr8w==", "engines": {"node": "*"}}, "node_modules/mozjpeg": {"version": "7.1.1", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "dependencies": {"bin-build": "^3.0.0", "bin-wrapper": "^4.0.0"}, "bin": {"mozjpeg": "cli.js"}, "engines": {"node": ">=10"}}, "node_modules/ms": {"version": "2.1.2", "license": "MIT"}, "node_modules/multicast-dns": {"version": "6.2.3", "dev": true, "license": "MIT", "dependencies": {"dns-packet": "^1.3.1", "thunky": "^1.0.2"}, "bin": {"multicast-dns": "cli.js"}}, "node_modules/multicast-dns-service-types": {"version": "1.1.0", "dev": true, "license": "MIT"}, "node_modules/nanoid": {"version": "3.3.1", "dev": true, "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/negotiator": {"version": "0.6.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/neo-async": {"version": "2.6.2", "dev": true, "license": "MIT"}, "node_modules/nice-try": {"version": "1.0.5", "dev": true, "license": "MIT", "optional": true}, "node_modules/nipplejs": {"version": "0.10.1", "resolved": "https://registry.npmjs.org/nipplejs/-/nipplejs-0.10.1.tgz", "integrity": "sha512-BuKBDfdd7BVK6E7sivHwrRPh9TETsHuHEwuT95nAjRz2uJu5roYngNs+BdRe8nYf8mP6OZ9aRqdgMlqVsDMRcw=="}, "node_modules/no-case": {"version": "3.0.4", "dev": true, "license": "MIT", "dependencies": {"lower-case": "^2.0.2", "tslib": "^2.0.3"}}, "node_modules/node-domexception": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/node-domexception/-/node-domexception-1.0.0.tgz", "integrity": "sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "github", "url": "https://paypal.me/jimmywarting"}], "engines": {"node": ">=10.5.0"}}, "node_modules/node-forge": {"version": "1.3.0", "dev": true, "license": "(BSD-3-<PERSON><PERSON> OR GPL-2.0)", "engines": {"node": ">= 6.13.0"}}, "node_modules/node-releases": {"version": "1.1.75", "license": "MIT"}, "node_modules/normalize-package-data": {"version": "3.0.3", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"hosted-git-info": "^4.0.1", "is-core-module": "^2.5.0", "semver": "^7.3.4", "validate-npm-package-license": "^3.0.1"}, "engines": {"node": ">=10"}}, "node_modules/normalize-path": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/normalize-url": {"version": "2.0.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"prepend-http": "^2.0.0", "query-string": "^5.0.1", "sort-keys": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/normalize-url/node_modules/prepend-http": {"version": "2.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/normalize-url/node_modules/sort-keys": {"version": "2.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"is-plain-obj": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npm-conf": {"version": "1.1.3", "dev": true, "license": "MIT", "optional": true, "dependencies": {"config-chain": "^1.1.11", "pify": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npm-conf/node_modules/pify": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/npm-run-path": {"version": "4.0.1", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/nth-check": {"version": "2.0.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0"}, "funding": {"url": "https://github.com/fb55/nth-check?sponsor=1"}}, "node_modules/object-assign": {"version": "4.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-is": {"version": "1.1.5", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-keys": {"version": "1.1.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/obuf": {"version": "1.1.2", "dev": true, "license": "MIT"}, "node_modules/on-finished": {"version": "2.3.0", "dev": true, "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/on-headers": {"version": "1.0.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.4.0", "dev": true, "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/onetime": {"version": "5.1.2", "dev": true, "license": "MIT", "dependencies": {"mimic-fn": "^2.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/open": {"version": "8.2.1", "dev": true, "license": "MIT", "dependencies": {"define-lazy-prop": "^2.0.0", "is-docker": "^2.1.1", "is-wsl": "^2.2.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/open-cli": {"version": "7.0.1", "dev": true, "license": "MIT", "dependencies": {"file-type": "^16.5.0", "get-stdin": "^9.0.0", "meow": "^10.0.1", "open": "^8.2.0", "tempy": "^1.0.1"}, "bin": {"open-cli": "cli.js"}, "engines": {"node": ">=14.13"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/optipng-bin": {"version": "7.0.1", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "dependencies": {"bin-build": "^3.0.0", "bin-wrapper": "^4.0.0"}, "bin": {"optipng": "cli.js"}, "engines": {"node": ">=10"}}, "node_modules/os-filter-obj": {"version": "2.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"arch": "^2.1.0"}, "engines": {"node": ">=4"}}, "node_modules/ow": {"version": "0.17.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"type-fest": "^0.11.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ow/node_modules/type-fest": {"version": "0.11.0", "dev": true, "license": "(MIT OR CC0-1.0)", "optional": true, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-cancelable": {"version": "0.3.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/p-event": {"version": "1.3.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"p-timeout": "^1.1.1"}, "engines": {"node": ">=4"}}, "node_modules/p-finally": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/p-is-promise": {"version": "1.1.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/p-limit": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-map": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"aggregate-error": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-map-series": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"p-reduce": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/p-pipe": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-reduce": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/p-retry": {"version": "4.6.1", "dev": true, "license": "MIT", "dependencies": {"@types/retry": "^0.12.0", "retry": "^0.13.1"}, "engines": {"node": ">=8"}}, "node_modules/p-timeout": {"version": "1.2.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"p-finally": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/p-try": {"version": "2.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/pako": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/pako/-/pako-2.1.0.tgz", "integrity": "sha512-w+eufiZ1WuJYgPXbV/PO3NCMEc3xqylkKHzp8bxp1uW4qaSNQUkwmLLEc3kKsfz8lpV1F8Ht3U1Cm+9Srog2ug=="}, "node_modules/param-case": {"version": "3.0.4", "dev": true, "license": "MIT", "dependencies": {"dot-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/parse-json": {"version": "5.2.0", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/parse5": {"version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"entities": "^4.3.0"}, "funding": {"url": "https://github.com/inikulin/parse5?sponsor=1"}}, "node_modules/parse5/node_modules/entities": {"version": "4.3.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.12"}, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/parseurl": {"version": "1.3.3", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/pascal-case": {"version": "3.1.2", "dev": true, "license": "MIT", "dependencies": {"no-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/path-exists": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-key": {"version": "3.1.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-parse": {"version": "1.0.7", "dev": true, "license": "MIT"}, "node_modules/path-to-regexp": {"version": "0.1.7", "dev": true, "license": "MIT"}, "node_modules/path-type": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/peek-readable": {"version": "4.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"type": "github", "url": "https://github.com/sponsors/Borewit"}}, "node_modules/pend": {"version": "1.2.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/picocolors": {"version": "1.0.0", "dev": true, "license": "ISC", "optional": true}, "node_modules/picomatch": {"version": "2.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pify": {"version": "4.0.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=6"}}, "node_modules/pinkie": {"version": "2.0.4", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/pinkie-promise": {"version": "2.0.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"pinkie": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/pkg-dir": {"version": "4.2.0", "dev": true, "license": "MIT", "dependencies": {"find-up": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/pkg-dir/node_modules/find-up": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/pkg-dir/node_modules/locate-path": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/pkg-dir/node_modules/p-limit": {"version": "2.3.0", "dev": true, "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/pkg-dir/node_modules/p-locate": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "node_modules/pngquant-bin": {"version": "6.0.1", "dev": true, "hasInstallScript": true, "license": "GPL-3.0+", "optional": true, "dependencies": {"bin-build": "^3.0.0", "bin-wrapper": "^4.0.1", "execa": "^4.0.0"}, "bin": {"pngquant": "cli.js"}, "engines": {"node": ">=10"}}, "node_modules/pngquant-bin/node_modules/execa": {"version": "4.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"cross-spawn": "^7.0.0", "get-stream": "^5.0.0", "human-signals": "^1.1.1", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "onetime": "^5.1.0", "signal-exit": "^3.0.2", "strip-final-newline": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/pngquant-bin/node_modules/get-stream": {"version": "5.2.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/pngquant-bin/node_modules/human-signals": {"version": "1.1.1", "dev": true, "license": "Apache-2.0", "optional": true, "engines": {"node": ">=8.12.0"}}, "node_modules/portfinder": {"version": "1.0.28", "dev": true, "license": "MIT", "dependencies": {"async": "^2.6.2", "debug": "^3.1.1", "mkdirp": "^0.5.5"}, "engines": {"node": ">= 0.12.0"}}, "node_modules/portfinder/node_modules/debug": {"version": "3.2.7", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/postcss": {"version": "8.3.6", "dev": true, "license": "MIT", "dependencies": {"colorette": "^1.2.2", "nanoid": "^3.1.23", "source-map-js": "^0.6.2"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}}, "node_modules/postcss-modules-extract-imports": {"version": "3.0.0", "dev": true, "license": "ISC", "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-modules-local-by-default": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"icss-utils": "^5.0.0", "postcss-selector-parser": "^6.0.2", "postcss-value-parser": "^4.1.0"}, "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-modules-scope": {"version": "3.0.0", "dev": true, "license": "ISC", "dependencies": {"postcss-selector-parser": "^6.0.4"}, "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-modules-values": {"version": "4.0.0", "dev": true, "license": "ISC", "dependencies": {"icss-utils": "^5.0.0"}, "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-selector-parser": {"version": "6.0.6", "dev": true, "license": "MIT", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "engines": {"node": ">=4"}}, "node_modules/postcss-value-parser": {"version": "4.1.0", "dev": true, "license": "MIT"}, "node_modules/prepend-http": {"version": "1.0.4", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/pretty-error": {"version": "3.0.4", "dev": true, "license": "MIT", "dependencies": {"lodash": "^4.17.20", "renderkid": "^2.0.6"}}, "node_modules/process": {"version": "0.11.10", "resolved": "https://registry.npmjs.org/process/-/process-0.11.10.tgz", "integrity": "sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A==", "engines": {"node": ">= 0.6.0"}}, "node_modules/process-nextick-args": {"version": "2.0.1", "dev": true, "license": "MIT"}, "node_modules/proto-list": {"version": "1.2.4", "dev": true, "license": "ISC", "optional": true}, "node_modules/proxy-addr": {"version": "2.0.7", "dev": true, "license": "MIT", "dependencies": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/proxy-from-env": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz", "integrity": "sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg=="}, "node_modules/pseudomap": {"version": "1.0.2", "dev": true, "license": "ISC", "optional": true}, "node_modules/pump": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/punycode": {"version": "2.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/qs": {"version": "6.7.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.6"}}, "node_modules/query-string": {"version": "5.1.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"decode-uri-component": "^0.2.0", "object-assign": "^4.1.0", "strict-uri-encode": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/queue-microtask": {"version": "1.2.3", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/quick-lru": {"version": "5.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/randombytes": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "^5.1.0"}}, "node_modules/range-parser": {"version": "1.2.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "2.4.0", "dev": true, "license": "MIT", "dependencies": {"bytes": "3.1.0", "http-errors": "1.7.2", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/raw-body/node_modules/bytes": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/read-pkg": {"version": "6.0.0", "dev": true, "license": "MIT", "dependencies": {"@types/normalize-package-data": "^2.4.0", "normalize-package-data": "^3.0.2", "parse-json": "^5.2.0", "type-fest": "^1.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/read-pkg-up": {"version": "8.0.0", "dev": true, "license": "MIT", "dependencies": {"find-up": "^5.0.0", "read-pkg": "^6.0.0", "type-fest": "^1.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/readable-stream": {"version": "3.6.0", "dev": true, "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/readable-web-to-node-stream": {"version": "3.0.2", "dev": true, "license": "MIT", "dependencies": {"readable-stream": "^3.6.0"}, "engines": {"node": ">=8"}, "funding": {"type": "github", "url": "https://github.com/sponsors/Borewit"}}, "node_modules/readdirp": {"version": "3.6.0", "dev": true, "license": "MIT", "dependencies": {"picomatch": "^2.2.1"}, "engines": {"node": ">=8.10.0"}}, "node_modules/rechoir": {"version": "0.7.1", "dev": true, "license": "MIT", "dependencies": {"resolve": "^1.9.0"}, "engines": {"node": ">= 0.10"}}, "node_modules/redent": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"indent-string": "^5.0.0", "strip-indent": "^4.0.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/regexp.prototype.flags": {"version": "1.3.1", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/relateurl": {"version": "0.2.7", "dev": true, "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/renderkid": {"version": "2.0.7", "dev": true, "license": "MIT", "dependencies": {"css-select": "^4.1.3", "dom-converter": "^0.2.0", "htmlparser2": "^6.1.0", "lodash": "^4.17.21", "strip-ansi": "^3.0.1"}}, "node_modules/replace-ext": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/require-from-string": {"version": "2.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/requires-port": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/resolve": {"version": "1.20.0", "dev": true, "license": "MIT", "dependencies": {"is-core-module": "^2.2.0", "path-parse": "^1.0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-cwd": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"resolve-from": "^5.0.0"}, "engines": {"node": ">=8"}}, "node_modules/resolve-from": {"version": "5.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/responselike": {"version": "1.0.2", "dev": true, "license": "MIT", "optional": true, "dependencies": {"lowercase-keys": "^1.0.0"}}, "node_modules/retry": {"version": "0.13.1", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/reusify": {"version": "1.0.4", "dev": true, "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/rimraf": {"version": "3.0.2", "dev": true, "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/rtcpeerconnection-shim": {"version": "1.2.15", "resolved": "https://registry.npmjs.org/rtcpeerconnection-shim/-/rtcpeerconnection-shim-1.2.15.tgz", "integrity": "sha512-C6DxhXt7bssQ1nHb154lqeL0SXz5Dx4RczXZu2Aa/L1NJFnEVDxFwCBo3fqtuljhHIGceg5JKBV4XJ0gW5JKyw==", "dependencies": {"sdp": "^2.6.0"}, "engines": {"node": ">=6.0.0", "npm": ">=3.10.0"}}, "node_modules/run-parallel": {"version": "1.2.0", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/safe-buffer": {"version": "5.2.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/safer-buffer": {"version": "2.1.2", "dev": true, "license": "MIT"}, "node_modules/schema-utils": {"version": "3.1.1", "dev": true, "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.8", "ajv": "^6.12.5", "ajv-keywords": "^3.5.2"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/sdp": {"version": "2.12.0", "resolved": "https://registry.npmjs.org/sdp/-/sdp-2.12.0.tgz", "integrity": "sha512-jhXqQAQVM+8Xj5EjJGVweuEzgtGWb3tmEEpl3CLP3cStInSbVHSg0QWOGQzNq8pSID4JkpeV2mPqlMDLrm0/Vw=="}, "node_modules/seek-bzip": {"version": "1.0.6", "dev": true, "license": "MIT", "optional": true, "dependencies": {"commander": "^2.8.1"}, "bin": {"seek-bunzip": "bin/seek-bunzip", "seek-table": "bin/seek-bzip-table"}}, "node_modules/seek-bzip/node_modules/commander": {"version": "2.20.3", "dev": true, "license": "MIT", "optional": true}, "node_modules/select-hose": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/selfsigned": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"node-forge": "^1.2.0"}, "engines": {"node": ">=10"}}, "node_modules/semver": {"version": "7.3.5", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/semver-regex": {"version": "2.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=6"}}, "node_modules/semver-truncate": {"version": "1.1.2", "dev": true, "license": "MIT", "optional": true, "dependencies": {"semver": "^5.3.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/semver-truncate/node_modules/semver": {"version": "5.7.1", "dev": true, "license": "ISC", "optional": true, "bin": {"semver": "bin/semver"}}, "node_modules/send": {"version": "0.17.1", "dev": true, "license": "MIT", "dependencies": {"debug": "2.6.9", "depd": "~1.1.2", "destroy": "~1.0.4", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "~1.7.2", "mime": "1.6.0", "ms": "2.1.1", "on-finished": "~2.3.0", "range-parser": "~1.2.1", "statuses": "~1.5.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/send/node_modules/debug": {"version": "2.6.9", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/send/node_modules/debug/node_modules/ms": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/send/node_modules/ms": {"version": "2.1.1", "dev": true, "license": "MIT"}, "node_modules/serialize-javascript": {"version": "6.0.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"randombytes": "^2.1.0"}}, "node_modules/serve-index": {"version": "1.9.1", "dev": true, "license": "MIT", "dependencies": {"accepts": "~1.3.4", "batch": "0.6.1", "debug": "2.6.9", "escape-html": "~1.0.3", "http-errors": "~1.6.2", "mime-types": "~2.1.17", "parseurl": "~1.3.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/serve-index/node_modules/debug": {"version": "2.6.9", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/serve-index/node_modules/http-errors": {"version": "1.6.3", "dev": true, "license": "MIT", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.0", "statuses": ">= 1.4.0 < 2"}, "engines": {"node": ">= 0.6"}}, "node_modules/serve-index/node_modules/inherits": {"version": "2.0.3", "dev": true, "license": "ISC"}, "node_modules/serve-index/node_modules/ms": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/serve-index/node_modules/setprototypeof": {"version": "1.1.0", "dev": true, "license": "ISC"}, "node_modules/serve-static": {"version": "1.14.1", "dev": true, "license": "MIT", "dependencies": {"encodeurl": "~1.0.2", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.17.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/setprototypeof": {"version": "1.1.1", "dev": true, "license": "ISC"}, "node_modules/shallow-clone": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^6.0.2"}, "engines": {"node": ">=8"}}, "node_modules/shebang-command": {"version": "2.0.0", "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/signal-exit": {"version": "3.0.3", "dev": true, "license": "ISC"}, "node_modules/slash": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/sockjs": {"version": "0.3.21", "dev": true, "license": "MIT", "dependencies": {"faye-websocket": "^0.11.3", "uuid": "^3.4.0", "websocket-driver": "^0.7.4"}}, "node_modules/sort-keys": {"version": "1.1.2", "dev": true, "license": "MIT", "optional": true, "dependencies": {"is-plain-obj": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/sort-keys-length": {"version": "1.0.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"sort-keys": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/source-map": {"version": "0.6.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-js": {"version": "0.6.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-support": {"version": "0.5.21", "dev": true, "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "node_modules/spdx-correct": {"version": "3.1.1", "dev": true, "license": "Apache-2.0", "dependencies": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}}, "node_modules/spdx-exceptions": {"version": "2.3.0", "dev": true, "license": "CC-BY-3.0"}, "node_modules/spdx-expression-parse": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "node_modules/spdx-license-ids": {"version": "3.0.10", "dev": true, "license": "CC0-1.0"}, "node_modules/spdy": {"version": "4.0.2", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.1.0", "handle-thing": "^2.0.0", "http-deceiver": "^1.2.7", "select-hose": "^2.0.0", "spdy-transport": "^3.0.0"}, "engines": {"node": ">=6.0.0"}}, "node_modules/spdy-transport": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.1.0", "detect-node": "^2.0.4", "hpack.js": "^2.1.6", "obuf": "^1.1.2", "readable-stream": "^3.0.6", "wbuf": "^1.7.3"}}, "node_modules/stable": {"version": "0.1.8", "dev": true, "license": "MIT", "optional": true}, "node_modules/statuses": {"version": "1.5.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/strict-uri-encode": {"version": "1.1.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/string_decoder": {"version": "1.3.0", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/strip-ansi": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/strip-dirs": {"version": "2.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"is-natural-number": "^4.0.1"}}, "node_modules/strip-eof": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/strip-final-newline": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/strip-indent": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"min-indent": "^1.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/strip-outer": {"version": "1.0.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"escape-string-regexp": "^1.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/strnum": {"version": "1.0.5", "dev": true, "license": "MIT", "optional": true}, "node_modules/strtok3": {"version": "6.2.4", "dev": true, "license": "MIT", "dependencies": {"@tokenizer/token": "^0.3.0", "peek-readable": "^4.0.1"}, "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/Borewit"}}, "node_modules/style-loader": {"version": "3.2.1", "dev": true, "license": "MIT", "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.0.0"}}, "node_modules/supports-color": {"version": "5.5.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/svgo": {"version": "2.8.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@trysound/sax": "0.2.0", "commander": "^7.2.0", "css-select": "^4.1.3", "css-tree": "^1.1.3", "csso": "^4.2.0", "picocolors": "^1.0.0", "stable": "^0.1.8"}, "bin": {"svgo": "bin/svgo"}, "engines": {"node": ">=10.13.0"}}, "node_modules/svgo/node_modules/commander": {"version": "7.2.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">= 10"}}, "node_modules/tapable": {"version": "2.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/tar-stream": {"version": "1.6.2", "dev": true, "license": "MIT", "optional": true, "dependencies": {"bl": "^1.0.0", "buffer-alloc": "^1.2.0", "end-of-stream": "^1.0.0", "fs-constants": "^1.0.0", "readable-stream": "^2.3.0", "to-buffer": "^1.1.1", "xtend": "^4.0.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/tar-stream/node_modules/readable-stream": {"version": "2.3.7", "dev": true, "license": "MIT", "optional": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/tar-stream/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "license": "MIT", "optional": true}, "node_modules/tar-stream/node_modules/string_decoder": {"version": "1.1.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/temp-dir": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/tempfile": {"version": "2.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"temp-dir": "^1.0.0", "uuid": "^3.0.1"}, "engines": {"node": ">=4"}}, "node_modules/tempfile/node_modules/temp-dir": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/tempy": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"del": "^6.0.0", "is-stream": "^2.0.0", "temp-dir": "^2.0.0", "type-fest": "^0.16.0", "unique-string": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/tempy/node_modules/type-fest": {"version": "0.16.0", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/terser": {"version": "4.8.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"commander": "^2.20.0", "source-map": "~0.6.1", "source-map-support": "~0.5.12"}, "bin": {"terser": "bin/terser"}, "engines": {"node": ">=6.0.0"}}, "node_modules/terser-webpack-plugin": {"version": "5.2.2", "dev": true, "license": "MIT", "dependencies": {"jest-worker": "^27.0.6", "p-limit": "^3.1.0", "schema-utils": "^3.1.1", "serialize-javascript": "^6.0.0", "source-map": "^0.6.1", "terser": "^5.7.2"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.1.0"}, "peerDependenciesMeta": {"@swc/core": {"optional": true}, "esbuild": {"optional": true}, "uglify-js": {"optional": true}}}, "node_modules/terser-webpack-plugin/node_modules/commander": {"version": "2.20.3", "dev": true, "license": "MIT"}, "node_modules/terser-webpack-plugin/node_modules/terser": {"version": "5.7.2", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"commander": "^2.20.0", "source-map": "~0.7.2", "source-map-support": "~0.5.19"}, "bin": {"terser": "bin/terser"}, "engines": {"node": ">=10"}}, "node_modules/terser-webpack-plugin/node_modules/terser/node_modules/source-map": {"version": "0.7.3", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">= 8"}}, "node_modules/terser/node_modules/commander": {"version": "2.20.3", "dev": true, "license": "MIT"}, "node_modules/through": {"version": "2.3.8", "dev": true, "license": "MIT", "optional": true}, "node_modules/thunky": {"version": "1.1.0", "dev": true, "license": "MIT"}, "node_modules/timed-out": {"version": "4.0.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/to-buffer": {"version": "1.1.1", "dev": true, "license": "MIT", "optional": true}, "node_modules/to-regex-range": {"version": "5.0.1", "dev": true, "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/toidentifier": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/token-types": {"version": "4.1.1", "dev": true, "license": "MIT", "dependencies": {"@tokenizer/token": "^0.3.0", "ieee754": "^1.2.1"}, "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/Borewit"}}, "node_modules/trim-newlines": {"version": "4.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/trim-repeated": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"escape-string-regexp": "^1.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ts-loader": {"version": "9.2.5", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.1.0", "enhanced-resolve": "^5.0.0", "micromatch": "^4.0.0", "semver": "^7.3.4"}, "engines": {"node": ">=12.0.0"}, "peerDependencies": {"typescript": "*", "webpack": "^5.0.0"}}, "node_modules/ts-loader/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/ts-loader/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/ts-loader/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/ts-loader/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/ts-loader/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ts-loader/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/tslib": {"version": "2.3.1", "dev": true, "license": "0BSD"}, "node_modules/tunnel-agent": {"version": "0.6.0", "dev": true, "license": "Apache-2.0", "optional": true, "dependencies": {"safe-buffer": "^5.0.1"}, "engines": {"node": "*"}}, "node_modules/type-fest": {"version": "1.4.0", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/type-is": {"version": "1.6.18", "dev": true, "license": "MIT", "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "engines": {"node": ">= 0.6"}}, "node_modules/typescript": {"version": "4.4.2", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=4.2.0"}}, "node_modules/ua-parser-js": {"version": "0.7.38", "resolved": "https://registry.npmjs.org/ua-parser-js/-/ua-parser-js-0.7.38.tgz", "integrity": "sha512-fYmIy7fKTSFAhG3fuPlubeGaMoAd6r0rSnfEsO5nEY55i26KSLt9EH7PLQiiqPUhNqYIJvSkTy1oArIcXAbPbA==", "funding": [{"type": "opencollective", "url": "https://opencollective.com/ua-parser-js"}, {"type": "paypal", "url": "https://paypal.me/faisalman"}, {"type": "github", "url": "https://github.com/sponsors/faisalman"}], "engines": {"node": "*"}}, "node_modules/unbzip2-stream": {"version": "1.4.3", "dev": true, "license": "MIT", "optional": true, "dependencies": {"buffer": "^5.2.1", "through": "^2.3.8"}}, "node_modules/unique-string": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"crypto-random-string": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/unpipe": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/uri-js": {"version": "4.4.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/url-parse-lax": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"prepend-http": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/url-to-options": {"version": "1.0.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">= 4"}}, "node_modules/util-deprecate": {"version": "1.0.2", "dev": true, "license": "MIT"}, "node_modules/utila": {"version": "0.4.0", "dev": true, "license": "MIT"}, "node_modules/utils-merge": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/uuid": {"version": "3.4.0", "dev": true, "license": "MIT", "bin": {"uuid": "bin/uuid"}}, "node_modules/validate-npm-package-license": {"version": "3.0.4", "dev": true, "license": "Apache-2.0", "dependencies": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}}, "node_modules/vary": {"version": "1.1.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/watchpack": {"version": "2.2.0", "dev": true, "license": "MIT", "dependencies": {"glob-to-regexp": "^0.4.1", "graceful-fs": "^4.1.2"}, "engines": {"node": ">=10.13.0"}}, "node_modules/wbuf": {"version": "1.7.3", "dev": true, "license": "MIT", "dependencies": {"minimalistic-assert": "^1.0.0"}}, "node_modules/web-streams-polyfill": {"version": "3.3.3", "resolved": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.3.3.tgz", "integrity": "sha512-d2JWLCivmZYTSIoge9MsgFCZrt571BikcWGYkjC1khllbTeDlGqZ2D8vD8E/lJa8WGWbb7Plm8/XJYV7IJHZZw==", "engines": {"node": ">= 8"}}, "node_modules/webpack": {"version": "5.51.2", "dev": true, "license": "MIT", "dependencies": {"@types/eslint-scope": "^3.7.0", "@types/estree": "^0.0.50", "@webassemblyjs/ast": "1.11.1", "@webassemblyjs/wasm-edit": "1.11.1", "@webassemblyjs/wasm-parser": "1.11.1", "acorn": "^8.4.1", "acorn-import-assertions": "^1.7.6", "browserslist": "^4.14.5", "chrome-trace-event": "^1.0.2", "enhanced-resolve": "^5.8.0", "es-module-lexer": "^0.7.1", "eslint-scope": "5.1.1", "events": "^3.2.0", "glob-to-regexp": "^0.4.1", "graceful-fs": "^4.2.4", "json-parse-better-errors": "^1.0.2", "loader-runner": "^4.2.0", "mime-types": "^2.1.27", "neo-async": "^2.6.2", "schema-utils": "^3.1.0", "tapable": "^2.1.1", "terser-webpack-plugin": "^5.1.3", "watchpack": "^2.2.0", "webpack-sources": "^3.2.0"}, "bin": {"webpack": "bin/webpack.js"}, "engines": {"node": ">=10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependenciesMeta": {"webpack-cli": {"optional": true}}}, "node_modules/webpack-cli": {"version": "4.10.0", "resolved": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.10.0.tgz", "integrity": "sha512-NLhDfH/h4O6UOy+0LSso42xvYypClINuMNBVVzX4vX98TmTaTUxwRbXdhucbFMd2qLaCTcLq/PdYrvi8onw90w==", "dev": true, "dependencies": {"@discoveryjs/json-ext": "^0.5.0", "@webpack-cli/configtest": "^1.2.0", "@webpack-cli/info": "^1.5.0", "@webpack-cli/serve": "^1.7.0", "colorette": "^2.0.14", "commander": "^7.0.0", "cross-spawn": "^7.0.3", "fastest-levenshtein": "^1.0.12", "import-local": "^3.0.2", "interpret": "^2.2.0", "rechoir": "^0.7.0", "webpack-merge": "^5.7.3"}, "bin": {"webpack-cli": "bin/cli.js"}, "engines": {"node": ">=10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "4.x.x || 5.x.x"}, "peerDependenciesMeta": {"@webpack-cli/generators": {"optional": true}, "@webpack-cli/migrate": {"optional": true}, "webpack-bundle-analyzer": {"optional": true}, "webpack-dev-server": {"optional": true}}}, "node_modules/webpack-cli/node_modules/colorette": {"version": "2.0.20", "resolved": "https://registry.npmjs.org/colorette/-/colorette-2.0.20.tgz", "integrity": "sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w==", "dev": true}, "node_modules/webpack-cli/node_modules/commander": {"version": "7.2.0", "dev": true, "license": "MIT", "engines": {"node": ">= 10"}}, "node_modules/webpack-dev-middleware": {"version": "5.3.1", "dev": true, "license": "MIT", "dependencies": {"colorette": "^2.0.10", "memfs": "^3.4.1", "mime-types": "^2.1.31", "range-parser": "^1.2.1", "schema-utils": "^4.0.0"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}}, "node_modules/webpack-dev-middleware/node_modules/ajv": {"version": "8.10.0", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/webpack-dev-middleware/node_modules/ajv-keywords": {"version": "5.1.0", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3"}, "peerDependencies": {"ajv": "^8.8.2"}}, "node_modules/webpack-dev-middleware/node_modules/colorette": {"version": "2.0.16", "dev": true, "license": "MIT"}, "node_modules/webpack-dev-middleware/node_modules/json-schema-traverse": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/webpack-dev-middleware/node_modules/schema-utils": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.9", "ajv": "^8.8.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.0.0"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/webpack-dev-server": {"version": "4.7.4", "dev": true, "license": "MIT", "dependencies": {"@types/bonjour": "^3.5.9", "@types/connect-history-api-fallback": "^1.3.5", "@types/express": "^4.17.13", "@types/serve-index": "^1.9.1", "@types/sockjs": "^0.3.33", "@types/ws": "^8.2.2", "ansi-html-community": "^0.0.8", "bonjour": "^3.5.0", "chokidar": "^3.5.3", "colorette": "^2.0.10", "compression": "^1.7.4", "connect-history-api-fallback": "^1.6.0", "default-gateway": "^6.0.3", "del": "^6.0.0", "express": "^4.17.1", "graceful-fs": "^4.2.6", "html-entities": "^2.3.2", "http-proxy-middleware": "^2.0.0", "ipaddr.js": "^2.0.1", "open": "^8.0.9", "p-retry": "^4.5.0", "portfinder": "^1.0.28", "schema-utils": "^4.0.0", "selfsigned": "^2.0.0", "serve-index": "^1.9.1", "sockjs": "^0.3.21", "spdy": "^4.0.2", "strip-ansi": "^7.0.0", "webpack-dev-middleware": "^5.3.1", "ws": "^8.4.2"}, "bin": {"webpack-dev-server": "bin/webpack-dev-server.js"}, "engines": {"node": ">= 12.13.0"}, "peerDependencies": {"webpack": "^4.37.0 || ^5.0.0"}, "peerDependenciesMeta": {"webpack-cli": {"optional": true}}}, "node_modules/webpack-dev-server/node_modules/ajv": {"version": "8.10.0", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/webpack-dev-server/node_modules/ajv-keywords": {"version": "5.1.0", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3"}, "peerDependencies": {"ajv": "^8.8.2"}}, "node_modules/webpack-dev-server/node_modules/ansi-regex": {"version": "6.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/webpack-dev-server/node_modules/colorette": {"version": "2.0.16", "dev": true, "license": "MIT"}, "node_modules/webpack-dev-server/node_modules/ipaddr.js": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 10"}}, "node_modules/webpack-dev-server/node_modules/json-schema-traverse": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/webpack-dev-server/node_modules/schema-utils": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.9", "ajv": "^8.8.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.0.0"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/webpack-dev-server/node_modules/strip-ansi": {"version": "7.0.1", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/webpack-merge": {"version": "5.8.0", "dev": true, "license": "MIT", "dependencies": {"clone-deep": "^4.0.1", "wildcard": "^2.0.0"}, "engines": {"node": ">=10.0.0"}}, "node_modules/webpack-sources": {"version": "3.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=10.13.0"}}, "node_modules/webrtc-adapter": {"version": "6.4.8", "resolved": "https://registry.npmjs.org/webrtc-adapter/-/webrtc-adapter-6.4.8.tgz", "integrity": "sha512-YM8yl545c/JhYcjGHgaCoA7jRK/KZuMwEDFeP2AcP0Auv5awEd+gZE0hXy9z7Ed3p9HvAXp8jdbe+4ESb1zxAw==", "dependencies": {"rtcpeerconnection-shim": "^1.2.14", "sdp": "^2.9.0"}, "engines": {"node": ">=6.0.0", "npm": ">=3.10.0"}}, "node_modules/websocket-driver": {"version": "0.7.4", "dev": true, "license": "Apache-2.0", "dependencies": {"http-parser-js": ">=0.5.1", "safe-buffer": ">=5.1.0", "websocket-extensions": ">=0.1.1"}, "engines": {"node": ">=0.8.0"}}, "node_modules/websocket-extensions": {"version": "0.1.4", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=0.8.0"}}, "node_modules/which": {"version": "2.0.2", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/wildcard": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/wrappy": {"version": "1.0.2", "dev": true, "license": "ISC"}, "node_modules/ws": {"version": "8.5.0", "dev": true, "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/xtend": {"version": "4.0.2", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.4"}}, "node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/yargs-parser": {"version": "20.2.9", "dev": true, "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yauzl": {"version": "2.10.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"buffer-crc32": "~0.2.3", "fd-slicer": "~1.1.0"}}, "node_modules/yocto-queue": {"version": "0.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}}, "dependencies": {"@agora-js/media": {"version": "4.21.0", "resolved": "https://registry.npmjs.org/@agora-js/media/-/media-4.21.0.tgz", "integrity": "sha512-X4aV84/gB4O7GOOkwP3+NGTHtT2IVkpa4DFBTlBNl7hrkjDwUeanzCQZyva92Zyw59N0NI6dKh9CjJKoIL5Now==", "requires": {"@agora-js/report": "4.21.0", "@agora-js/shared": "4.21.0", "agora-rte-extension": "^1.2.4", "axios": "^1.6.8", "pako": "^2.1.0", "webrtc-adapter": "8.2.0"}, "dependencies": {"sdp": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/sdp/-/sdp-3.2.0.tgz", "integrity": "sha512-d7wDPgDV3DDiqulJjKiV2865wKsJ34YI+NDREbm+FySq6WuKOikwyNQcm+doLAZ1O6ltdO0SeKle2xMpN3Brgw=="}, "webrtc-adapter": {"version": "8.2.0", "resolved": "https://registry.npmjs.org/webrtc-adapter/-/webrtc-adapter-8.2.0.tgz", "integrity": "sha512-umxCMgedPAVq4Pe/jl3xmelLXLn4XZWFEMR5Iipb5wJ+k1xMX0yC4ZY9CueZUU1MjapFxai1tFGE7R/kotH6Ww==", "requires": {"sdp": "^3.0.2"}}}}, "@agora-js/report": {"version": "4.21.0", "resolved": "https://registry.npmjs.org/@agora-js/report/-/report-4.21.0.tgz", "integrity": "sha512-c8KIdomuPItwvri431z5CPT7L4m+jLJrwWWt/QS3JN+sp/t49NnoOIyKQf3y5xCbyNPCNNeGofrwkaIRu4YE8g==", "requires": {"@agora-js/shared": "4.21.0", "axios": "^1.6.8"}}, "@agora-js/shared": {"version": "4.21.0", "resolved": "https://registry.npmjs.org/@agora-js/shared/-/shared-4.21.0.tgz", "integrity": "sha512-oqaiuIhG9ai/NXUDEmj9b3uGxxU9I0OZZszNaJexjakJuVZPM7ypzrCLUi5SzkTh++QOf68yuvD488fjq5WQsA==", "requires": {"axios": "^1.6.8", "ua-parser-js": "^0.7.34"}}, "@babel/code-frame": {"version": "7.14.5", "dev": true, "requires": {"@babel/highlight": "^7.14.5"}}, "@babel/helper-validator-identifier": {"version": "7.14.9", "dev": true}, "@babel/highlight": {"version": "7.14.5", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.5", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}}, "@discoveryjs/json-ext": {"version": "0.5.3", "dev": true}, "@jridgewell/gen-mapping": {"version": "0.3.2", "dev": true, "requires": {"@jridgewell/set-array": "^1.0.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.9"}}, "@jridgewell/resolve-uri": {"version": "3.1.0", "dev": true}, "@jridgewell/set-array": {"version": "1.1.2", "dev": true}, "@jridgewell/source-map": {"version": "0.3.2", "dev": true, "requires": {"@jridgewell/gen-mapping": "^0.3.0", "@jridgewell/trace-mapping": "^0.3.9"}}, "@jridgewell/sourcemap-codec": {"version": "1.4.14", "dev": true}, "@jridgewell/trace-mapping": {"version": "0.3.15", "dev": true, "requires": {"@jridgewell/resolve-uri": "^3.0.3", "@jridgewell/sourcemap-codec": "^1.4.10"}}, "@nodelib/fs.scandir": {"version": "2.1.5", "dev": true, "requires": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}}, "@nodelib/fs.stat": {"version": "2.0.5", "dev": true}, "@nodelib/fs.walk": {"version": "1.2.8", "dev": true, "requires": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}}, "@popperjs/core": {"version": "2.11.8", "resolved": "https://registry.npmjs.org/@popperjs/core/-/core-2.11.8.tgz", "integrity": "sha512-P1st0aksCrn9sGZhp8GMYwBnQsbvAWsZAX44oXNNvLHGqAOcoVxmjZiohstwQ7SqKnbR47akdNi+uleWD8+g6A=="}, "@sindresorhus/is": {"version": "0.7.0", "dev": true, "optional": true}, "@tensorworks/libspsfrontend": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/@tensorworks/libspsfrontend/-/libspsfrontend-0.1.4.tgz", "integrity": "sha512-EBLJynDpr3qQrzvnk0Kg/BQY4/VrscGgYcDP3ryvsGzHJBTV1kYp1lT0WROJG2E4WBDt5C976TU0R7Ll3FX34g==", "dev": true, "requires": {"events": "^3.3.0"}}, "@tokenizer/token": {"version": "0.3.0", "dev": true}, "@trysound/sax": {"version": "0.2.0", "dev": true, "optional": true}, "@types/body-parser": {"version": "1.19.2", "dev": true, "requires": {"@types/connect": "*", "@types/node": "*"}}, "@types/bonjour": {"version": "3.5.10", "dev": true, "requires": {"@types/node": "*"}}, "@types/bootstrap": {"version": "5.2.10", "resolved": "https://registry.npmjs.org/@types/bootstrap/-/bootstrap-5.2.10.tgz", "integrity": "sha512-F2X+cd6551tep0MvVZ6nM8v7XgGN/twpdNDjqS1TUM7YFNEtQYWk+dKAnH+T1gr6QgCoGMPl487xw/9hXooa2g==", "requires": {"@popperjs/core": "^2.9.2"}}, "@types/connect": {"version": "3.4.35", "dev": true, "requires": {"@types/node": "*"}}, "@types/connect-history-api-fallback": {"version": "1.3.5", "dev": true, "requires": {"@types/express-serve-static-core": "*", "@types/node": "*"}}, "@types/eslint": {"version": "7.28.0", "dev": true, "requires": {"@types/estree": "*", "@types/json-schema": "*"}}, "@types/eslint-scope": {"version": "3.7.1", "dev": true, "requires": {"@types/eslint": "*", "@types/estree": "*"}}, "@types/estree": {"version": "0.0.50", "dev": true}, "@types/express": {"version": "4.17.13", "dev": true, "requires": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.18", "@types/qs": "*", "@types/serve-static": "*"}}, "@types/express-serve-static-core": {"version": "4.17.28", "dev": true, "requires": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*"}}, "@types/glob": {"version": "7.2.0", "dev": true, "requires": {"@types/minimatch": "*", "@types/node": "*"}}, "@types/html-minifier-terser": {"version": "5.1.2", "dev": true}, "@types/http-proxy": {"version": "1.17.8", "dev": true, "requires": {"@types/node": "*"}}, "@types/jquery": {"version": "3.5.14", "resolved": "https://registry.npmjs.org/@types/jquery/-/jquery-3.5.14.tgz", "integrity": "sha512-X1gtMRMbziVQkErhTQmSe2jFwwENA/Zr+PprCkF63vFq+Yt5PZ4AlKqgmeNlwgn7dhsXEK888eIW2520EpC+xg==", "dev": true, "requires": {"@types/sizzle": "*"}}, "@types/json-schema": {"version": "7.0.9", "dev": true}, "@types/mime": {"version": "1.3.2", "dev": true}, "@types/minimatch": {"version": "3.0.5", "dev": true}, "@types/minimist": {"version": "1.2.2", "dev": true}, "@types/node": {"version": "16.7.10", "dev": true}, "@types/normalize-package-data": {"version": "2.4.1", "dev": true}, "@types/qs": {"version": "6.9.7", "dev": true}, "@types/range-parser": {"version": "1.2.4", "dev": true}, "@types/retry": {"version": "0.12.1", "dev": true}, "@types/serve-index": {"version": "1.9.1", "dev": true, "requires": {"@types/express": "*"}}, "@types/serve-static": {"version": "1.13.10", "dev": true, "requires": {"@types/mime": "^1", "@types/node": "*"}}, "@types/sizzle": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/@types/sizzle/-/sizzle-2.3.3.tgz", "integrity": "sha512-JYM8x9EGF163bEyhdJBpR2QX1R5naCJHC8ucJylJ3w9/CVBaskdQ8WqBf8MmQrd1kRvp/a4TS8HJ+bxzR7ZJYQ==", "dev": true}, "@types/sockjs": {"version": "0.3.33", "dev": true, "requires": {"@types/node": "*"}}, "@types/ws": {"version": "8.5.3", "dev": true, "requires": {"@types/node": "*"}}, "@voxeet/voxeet-web-sdk": {"version": "3.11.0-beta.1", "resolved": "https://registry.npmjs.org/@voxeet/voxeet-web-sdk/-/voxeet-web-sdk-3.11.0-beta.1.tgz", "integrity": "sha512-xyGvZn+STbrQ7b1MjiWHeFNaMrQ9sPlu3YeFmEmQxiG2JxLgP9mFRva//z6AERm66Y4swi7YW5XJ5rHpdWSNuQ==", "requires": {"axios": "^0.26.0", "bowser": "^2.8.1", "browserslist": "^4.5.2", "btoa": "^1.1.2", "debug": "^4.3.1", "events": "^1.1.0", "get-float-time-domain-data": "^0.1.0", "global": "^4.4.0", "jwt-decode": "3.1.2", "lodash.isequal": "4.5.0", "lodash.merge": "4.6.2", "moment": "^2.29.4", "sdp": "^2.3.0", "webrtc-adapter": "^6.3.2"}, "dependencies": {"axios": {"version": "0.26.1", "resolved": "https://registry.npmjs.org/axios/-/axios-0.26.1.tgz", "integrity": "sha512-fPwcX4EvnSHuInCMItEhAGnaSEXRBjtzh9fOtsE6E1G6p7vl7edEeZe11QHf18+6+9gR5PbKV/sGKNaD8YaMeA==", "requires": {"follow-redirects": "^1.14.8"}}, "events": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/events/-/events-1.1.1.tgz", "integrity": "sha512-kEcvvCBByWXGnZy6JUlgAp2gBIUjfCAV6P6TgT1/aaQKcmuAEC4OZTV1I4EWQLz2gxZw76atuVyvHhTxvi0Flw=="}}}, "@webassemblyjs/ast": {"version": "1.11.1", "dev": true, "requires": {"@webassemblyjs/helper-numbers": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1"}}, "@webassemblyjs/floating-point-hex-parser": {"version": "1.11.1", "dev": true}, "@webassemblyjs/helper-api-error": {"version": "1.11.1", "dev": true}, "@webassemblyjs/helper-buffer": {"version": "1.11.1", "dev": true}, "@webassemblyjs/helper-numbers": {"version": "1.11.1", "dev": true, "requires": {"@webassemblyjs/floating-point-hex-parser": "1.11.1", "@webassemblyjs/helper-api-error": "1.11.1", "@xtuc/long": "4.2.2"}}, "@webassemblyjs/helper-wasm-bytecode": {"version": "1.11.1", "dev": true}, "@webassemblyjs/helper-wasm-section": {"version": "1.11.1", "dev": true, "requires": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/helper-buffer": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1", "@webassemblyjs/wasm-gen": "1.11.1"}}, "@webassemblyjs/ieee754": {"version": "1.11.1", "dev": true, "requires": {"@xtuc/ieee754": "^1.2.0"}}, "@webassemblyjs/leb128": {"version": "1.11.1", "dev": true, "requires": {"@xtuc/long": "4.2.2"}}, "@webassemblyjs/utf8": {"version": "1.11.1", "dev": true}, "@webassemblyjs/wasm-edit": {"version": "1.11.1", "dev": true, "requires": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/helper-buffer": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1", "@webassemblyjs/helper-wasm-section": "1.11.1", "@webassemblyjs/wasm-gen": "1.11.1", "@webassemblyjs/wasm-opt": "1.11.1", "@webassemblyjs/wasm-parser": "1.11.1", "@webassemblyjs/wast-printer": "1.11.1"}}, "@webassemblyjs/wasm-gen": {"version": "1.11.1", "dev": true, "requires": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1", "@webassemblyjs/ieee754": "1.11.1", "@webassemblyjs/leb128": "1.11.1", "@webassemblyjs/utf8": "1.11.1"}}, "@webassemblyjs/wasm-opt": {"version": "1.11.1", "dev": true, "requires": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/helper-buffer": "1.11.1", "@webassemblyjs/wasm-gen": "1.11.1", "@webassemblyjs/wasm-parser": "1.11.1"}}, "@webassemblyjs/wasm-parser": {"version": "1.11.1", "dev": true, "requires": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/helper-api-error": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1", "@webassemblyjs/ieee754": "1.11.1", "@webassemblyjs/leb128": "1.11.1", "@webassemblyjs/utf8": "1.11.1"}}, "@webassemblyjs/wast-printer": {"version": "1.11.1", "dev": true, "requires": {"@webassemblyjs/ast": "1.11.1", "@xtuc/long": "4.2.2"}}, "@webpack-cli/configtest": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-1.2.0.tgz", "integrity": "sha512-4FB8Tj6xyVkyqjj1OaTqCjXYULB9FMkqQ8yGrZjRDrYh0nOE+7Lhs45WioWQQMV+ceFlE368Ukhe6xdvJM9Egg==", "dev": true, "requires": {}}, "@webpack-cli/info": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/@webpack-cli/info/-/info-1.5.0.tgz", "integrity": "sha512-e8tSXZpw2hPl2uMJY6fsMswaok5FdlGNRTktvFk2sD8RjH0hE2+XistawJx1vmKteh4NmGmNUrp+Tb2w+udPcQ==", "dev": true, "requires": {"envinfo": "^7.7.3"}}, "@webpack-cli/serve": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-1.7.0.tgz", "integrity": "sha512-oxnCNGj88fL+xzV+dacXs44HcDwf1ovs3AuEzvP7mqXw7fQntqIhQ1BRmynh4qEKQSSSRSWVyXRjmTbZIX9V2Q==", "dev": true, "requires": {}}, "@xtuc/ieee754": {"version": "1.2.0", "dev": true}, "@xtuc/long": {"version": "4.2.2", "dev": true}, "accepts": {"version": "1.3.7", "dev": true, "requires": {"mime-types": "~2.1.24", "negotiator": "0.6.2"}}, "acorn": {"version": "8.8.0", "dev": true}, "acorn-import-assertions": {"version": "1.7.6", "dev": true, "requires": {}}, "aggregate-error": {"version": "3.1.0", "dev": true, "requires": {"clean-stack": "^2.0.0", "indent-string": "^4.0.0"}, "dependencies": {"indent-string": {"version": "4.0.0", "dev": true}}}, "agora-rtc-sdk-ng": {"version": "4.21.0", "resolved": "https://registry.npmjs.org/agora-rtc-sdk-ng/-/agora-rtc-sdk-ng-4.21.0.tgz", "integrity": "sha512-EAZMdhbqIXl/PtFqEQn0O5Pmj3Tt+4oTXtd76MK1CozgbcDsc0TmFZK3qM25eaKqhzTz1wiVCwzBCWs3pF5OpQ==", "requires": {"@agora-js/media": "4.21.0", "@agora-js/report": "4.21.0", "@agora-js/shared": "4.21.0", "agora-rte-extension": "^1.2.4", "axios": "^1.6.8", "formdata-polyfill": "^4.0.7", "ua-parser-js": "^0.7.34", "webrtc-adapter": "8.2.0"}, "dependencies": {"sdp": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/sdp/-/sdp-3.2.0.tgz", "integrity": "sha512-d7wDPgDV3DDiqulJjKiV2865wKsJ34YI+NDREbm+FySq6WuKOikwyNQcm+doLAZ1O6ltdO0SeKle2xMpN3Brgw=="}, "webrtc-adapter": {"version": "8.2.0", "resolved": "https://registry.npmjs.org/webrtc-adapter/-/webrtc-adapter-8.2.0.tgz", "integrity": "sha512-umxCMgedPAVq4Pe/jl3xmelLXLn4XZWFEMR5Iipb5wJ+k1xMX0yC4ZY9CueZUU1MjapFxai1tFGE7R/kotH6Ww==", "requires": {"sdp": "^3.0.2"}}}}, "agora-rte-extension": {"version": "1.2.4", "resolved": "https://registry.npmjs.org/agora-rte-extension/-/agora-rte-extension-1.2.4.tgz", "integrity": "sha512-0ovZz1lbe30QraG1cU+ji7EnQ8aUu+Hf3F+a8xPml3wPOyUQEK6CTdxV9kMecr9t+fIDrGeW7wgJTsM1DQE7Nw=="}, "ajv": {"version": "6.12.6", "dev": true, "requires": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "ajv-formats": {"version": "2.1.1", "dev": true, "requires": {"ajv": "^8.0.0"}, "dependencies": {"ajv": {"version": "8.10.0", "dev": true, "requires": {"fast-deep-equal": "^3.1.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2", "uri-js": "^4.2.2"}}, "json-schema-traverse": {"version": "1.0.0", "dev": true}}}, "ajv-keywords": {"version": "3.5.2", "dev": true, "requires": {}}, "ansi-html-community": {"version": "0.0.8", "dev": true}, "ansi-regex": {"version": "2.1.1", "dev": true}, "ansi-styles": {"version": "3.2.1", "dev": true, "requires": {"color-convert": "^1.9.0"}}, "anymatch": {"version": "3.1.2", "dev": true, "requires": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}}, "arch": {"version": "2.2.0", "dev": true, "optional": true}, "archive-type": {"version": "4.0.0", "dev": true, "optional": true, "requires": {"file-type": "^4.2.0"}, "dependencies": {"file-type": {"version": "4.4.0", "dev": true, "optional": true}}}, "array-flatten": {"version": "2.1.2", "dev": true}, "array-union": {"version": "2.1.0", "dev": true}, "arrify": {"version": "1.0.1", "dev": true}, "async": {"version": "2.6.4", "dev": true, "requires": {"lodash": "^4.17.14"}}, "asynckit": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q=="}, "axios": {"version": "1.7.2", "resolved": "https://registry.npmjs.org/axios/-/axios-1.7.2.tgz", "integrity": "sha512-2A8QhOMrbomlDuiLeK9XibIBzuHeRcqqNOHp0Cyp5EoJ1IFDh+XZH3A6BkXtv0K4gFGCI0Y4BM7B1wOEi0Rmgw==", "requires": {"follow-redirects": "^1.15.6", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "balanced-match": {"version": "1.0.2", "dev": true}, "base64-js": {"version": "1.5.1", "dev": true, "optional": true}, "batch": {"version": "0.6.1", "dev": true}, "big.js": {"version": "5.2.2", "dev": true}, "bin-build": {"version": "3.0.0", "dev": true, "optional": true, "requires": {"decompress": "^4.0.0", "download": "^6.2.2", "execa": "^0.7.0", "p-map-series": "^1.0.0", "tempfile": "^2.0.0"}, "dependencies": {"cross-spawn": {"version": "5.1.0", "dev": true, "optional": true, "requires": {"lru-cache": "^4.0.1", "shebang-command": "^1.2.0", "which": "^1.2.9"}}, "execa": {"version": "0.7.0", "dev": true, "optional": true, "requires": {"cross-spawn": "^5.0.1", "get-stream": "^3.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}}, "get-stream": {"version": "3.0.0", "dev": true, "optional": true}, "is-stream": {"version": "1.1.0", "dev": true, "optional": true}, "lru-cache": {"version": "4.1.5", "dev": true, "optional": true, "requires": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "npm-run-path": {"version": "2.0.2", "dev": true, "optional": true, "requires": {"path-key": "^2.0.0"}}, "path-key": {"version": "2.0.1", "dev": true, "optional": true}, "shebang-command": {"version": "1.2.0", "dev": true, "optional": true, "requires": {"shebang-regex": "^1.0.0"}}, "shebang-regex": {"version": "1.0.0", "dev": true, "optional": true}, "which": {"version": "1.3.1", "dev": true, "optional": true, "requires": {"isexe": "^2.0.0"}}, "yallist": {"version": "2.1.2", "dev": true, "optional": true}}}, "bin-check": {"version": "4.1.0", "dev": true, "optional": true, "requires": {"execa": "^0.7.0", "executable": "^4.1.0"}, "dependencies": {"cross-spawn": {"version": "5.1.0", "dev": true, "optional": true, "requires": {"lru-cache": "^4.0.1", "shebang-command": "^1.2.0", "which": "^1.2.9"}}, "execa": {"version": "0.7.0", "dev": true, "optional": true, "requires": {"cross-spawn": "^5.0.1", "get-stream": "^3.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}}, "get-stream": {"version": "3.0.0", "dev": true, "optional": true}, "is-stream": {"version": "1.1.0", "dev": true, "optional": true}, "lru-cache": {"version": "4.1.5", "dev": true, "optional": true, "requires": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "npm-run-path": {"version": "2.0.2", "dev": true, "optional": true, "requires": {"path-key": "^2.0.0"}}, "path-key": {"version": "2.0.1", "dev": true, "optional": true}, "shebang-command": {"version": "1.2.0", "dev": true, "optional": true, "requires": {"shebang-regex": "^1.0.0"}}, "shebang-regex": {"version": "1.0.0", "dev": true, "optional": true}, "which": {"version": "1.3.1", "dev": true, "optional": true, "requires": {"isexe": "^2.0.0"}}, "yallist": {"version": "2.1.2", "dev": true, "optional": true}}}, "bin-version": {"version": "3.1.0", "dev": true, "optional": true, "requires": {"execa": "^1.0.0", "find-versions": "^3.0.0"}, "dependencies": {"cross-spawn": {"version": "6.0.5", "dev": true, "optional": true, "requires": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}}, "execa": {"version": "1.0.0", "dev": true, "optional": true, "requires": {"cross-spawn": "^6.0.0", "get-stream": "^4.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}}, "get-stream": {"version": "4.1.0", "dev": true, "optional": true, "requires": {"pump": "^3.0.0"}}, "is-stream": {"version": "1.1.0", "dev": true, "optional": true}, "npm-run-path": {"version": "2.0.2", "dev": true, "optional": true, "requires": {"path-key": "^2.0.0"}}, "path-key": {"version": "2.0.1", "dev": true, "optional": true}, "semver": {"version": "5.7.1", "dev": true, "optional": true}, "shebang-command": {"version": "1.2.0", "dev": true, "optional": true, "requires": {"shebang-regex": "^1.0.0"}}, "shebang-regex": {"version": "1.0.0", "dev": true, "optional": true}, "which": {"version": "1.3.1", "dev": true, "optional": true, "requires": {"isexe": "^2.0.0"}}}}, "bin-version-check": {"version": "4.0.0", "dev": true, "optional": true, "requires": {"bin-version": "^3.0.0", "semver": "^5.6.0", "semver-truncate": "^1.1.2"}, "dependencies": {"semver": {"version": "5.7.1", "dev": true, "optional": true}}}, "bin-wrapper": {"version": "4.1.0", "dev": true, "optional": true, "requires": {"bin-check": "^4.1.0", "bin-version-check": "^4.0.0", "download": "^7.1.0", "import-lazy": "^3.1.0", "os-filter-obj": "^2.0.0", "pify": "^4.0.1"}, "dependencies": {"download": {"version": "7.1.0", "dev": true, "optional": true, "requires": {"archive-type": "^4.0.0", "caw": "^2.0.1", "content-disposition": "^0.5.2", "decompress": "^4.2.0", "ext-name": "^5.0.0", "file-type": "^8.1.0", "filenamify": "^2.0.0", "get-stream": "^3.0.0", "got": "^8.3.1", "make-dir": "^1.2.0", "p-event": "^2.1.0", "pify": "^3.0.0"}, "dependencies": {"pify": {"version": "3.0.0", "dev": true, "optional": true}}}, "file-type": {"version": "8.1.0", "dev": true, "optional": true}, "get-stream": {"version": "3.0.0", "dev": true, "optional": true}, "got": {"version": "8.3.2", "dev": true, "optional": true, "requires": {"@sindresorhus/is": "^0.7.0", "cacheable-request": "^2.1.1", "decompress-response": "^3.3.0", "duplexer3": "^0.1.4", "get-stream": "^3.0.0", "into-stream": "^3.1.0", "is-retry-allowed": "^1.1.0", "isurl": "^1.0.0-alpha5", "lowercase-keys": "^1.0.0", "mimic-response": "^1.0.0", "p-cancelable": "^0.4.0", "p-timeout": "^2.0.1", "pify": "^3.0.0", "safe-buffer": "^5.1.1", "timed-out": "^4.0.1", "url-parse-lax": "^3.0.0", "url-to-options": "^1.0.1"}, "dependencies": {"pify": {"version": "3.0.0", "dev": true, "optional": true}}}, "make-dir": {"version": "1.3.0", "dev": true, "optional": true, "requires": {"pify": "^3.0.0"}, "dependencies": {"pify": {"version": "3.0.0", "dev": true, "optional": true}}}, "p-cancelable": {"version": "0.4.1", "dev": true, "optional": true}, "p-event": {"version": "2.3.1", "dev": true, "optional": true, "requires": {"p-timeout": "^2.0.1"}}, "p-timeout": {"version": "2.0.1", "dev": true, "optional": true, "requires": {"p-finally": "^1.0.0"}}, "prepend-http": {"version": "2.0.0", "dev": true, "optional": true}, "url-parse-lax": {"version": "3.0.0", "dev": true, "optional": true, "requires": {"prepend-http": "^2.0.0"}}}}, "binary-extensions": {"version": "2.2.0", "dev": true}, "bl": {"version": "1.2.3", "dev": true, "optional": true, "requires": {"readable-stream": "^2.3.5", "safe-buffer": "^5.1.1"}, "dependencies": {"readable-stream": {"version": "2.3.7", "dev": true, "optional": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "safe-buffer": {"version": "5.1.2", "dev": true, "optional": true}, "string_decoder": {"version": "1.1.1", "dev": true, "optional": true, "requires": {"safe-buffer": "~5.1.0"}}}}, "body-parser": {"version": "1.19.0", "dev": true, "requires": {"bytes": "3.1.0", "content-type": "~1.0.4", "debug": "2.6.9", "depd": "~1.1.2", "http-errors": "1.7.2", "iconv-lite": "0.4.24", "on-finished": "~2.3.0", "qs": "6.7.0", "raw-body": "2.4.0", "type-is": "~1.6.17"}, "dependencies": {"bytes": {"version": "3.1.0", "dev": true}, "debug": {"version": "2.6.9", "dev": true, "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "dev": true}}}, "bonjour": {"version": "3.5.0", "dev": true, "requires": {"array-flatten": "^2.1.0", "deep-equal": "^1.0.1", "dns-equal": "^1.0.0", "dns-txt": "^2.0.2", "multicast-dns": "^6.0.1", "multicast-dns-service-types": "^1.1.0"}}, "boolbase": {"version": "1.0.0", "dev": true}, "bootstrap": {"version": "5.3.0", "resolved": "https://registry.npmjs.org/bootstrap/-/bootstrap-5.3.0.tgz", "integrity": "sha512-UnBV3E3v4STVNQdms6jSGO2CvOkjUMdDAVR2V5N4uCMdaIkaQjbcEAMqRimDHIs4uqBYzDAKCQwCB+97tJgHQw==", "dev": true, "requires": {}}, "bowser": {"version": "2.11.0", "resolved": "https://registry.npmjs.org/bowser/-/bowser-2.11.0.tgz", "integrity": "sha512-AlcaJBi/pqqJBIQ8U9Mcpc9i8Aqxn88Skv5d+xBX006BY5u8N3mGLHa5Lgppa7L/HfwgwLgZ6NYs+Ag6uUmJRA=="}, "brace-expansion": {"version": "1.1.11", "dev": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "braces": {"version": "3.0.2", "dev": true, "requires": {"fill-range": "^7.0.1"}}, "browserslist": {"version": "4.16.8", "requires": {"caniuse-lite": "^1.0.30001251", "colorette": "^1.3.0", "electron-to-chromium": "^1.3.811", "escalade": "^3.1.1", "node-releases": "^1.1.75"}}, "btoa": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/btoa/-/btoa-1.2.1.tgz", "integrity": "sha512-SB4/MIGlsiVkMcHmT+pSmIPoNDoHg+7cMzmt3Uxt628MTz2487DKSqK/fuhFBrkuqrYv5UCEnACpF4dTFNKc/g=="}, "buffer": {"version": "5.7.1", "dev": true, "optional": true, "requires": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "buffer-alloc": {"version": "1.2.0", "dev": true, "optional": true, "requires": {"buffer-alloc-unsafe": "^1.1.0", "buffer-fill": "^1.0.0"}}, "buffer-alloc-unsafe": {"version": "1.1.0", "dev": true, "optional": true}, "buffer-crc32": {"version": "0.2.13", "dev": true, "optional": true}, "buffer-fill": {"version": "1.0.0", "dev": true, "optional": true}, "buffer-from": {"version": "1.1.2", "dev": true}, "buffer-indexof": {"version": "1.1.1", "dev": true}, "bytes": {"version": "3.0.0", "dev": true}, "cacheable-request": {"version": "2.1.4", "dev": true, "optional": true, "requires": {"clone-response": "1.0.2", "get-stream": "3.0.0", "http-cache-semantics": "3.8.1", "keyv": "3.0.0", "lowercase-keys": "1.0.0", "normalize-url": "2.0.1", "responselike": "1.0.2"}, "dependencies": {"get-stream": {"version": "3.0.0", "dev": true, "optional": true}, "lowercase-keys": {"version": "1.0.0", "dev": true, "optional": true}}}, "call-bind": {"version": "1.0.2", "dev": true, "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}}, "camel-case": {"version": "4.1.2", "dev": true, "requires": {"pascal-case": "^3.1.2", "tslib": "^2.0.3"}}, "camelcase": {"version": "6.2.0", "dev": true}, "camelcase-keys": {"version": "7.0.0", "dev": true, "requires": {"camelcase": "^6.2.0", "map-obj": "^4.1.0", "quick-lru": "^5.1.1", "type-fest": "^1.2.1"}}, "caniuse-lite": {"version": "1.0.30001252"}, "caw": {"version": "2.0.1", "dev": true, "optional": true, "requires": {"get-proxy": "^2.0.0", "isurl": "^1.0.0-alpha5", "tunnel-agent": "^0.6.0", "url-to-options": "^1.0.1"}}, "chalk": {"version": "2.4.2", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "chokidar": {"version": "3.5.3", "dev": true, "requires": {"anymatch": "~3.1.2", "braces": "~3.0.2", "fsevents": "~2.3.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}}, "chrome-trace-event": {"version": "1.0.3", "dev": true}, "clean-css": {"version": "4.2.3", "dev": true, "requires": {"source-map": "~0.6.0"}}, "clean-stack": {"version": "2.2.0", "dev": true}, "clone-deep": {"version": "4.0.1", "dev": true, "requires": {"is-plain-object": "^2.0.4", "kind-of": "^6.0.2", "shallow-clone": "^3.0.0"}}, "clone-response": {"version": "1.0.2", "dev": true, "optional": true, "requires": {"mimic-response": "^1.0.0"}}, "color-convert": {"version": "1.9.3", "dev": true, "requires": {"color-name": "1.1.3"}}, "color-name": {"version": "1.1.3", "dev": true}, "colorette": {"version": "1.3.0"}, "combined-stream": {"version": "1.0.8", "resolved": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz", "integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==", "requires": {"delayed-stream": "~1.0.0"}}, "commander": {"version": "4.1.1", "dev": true}, "compressible": {"version": "2.0.18", "dev": true, "requires": {"mime-db": ">= 1.43.0 < 2"}}, "compression": {"version": "1.7.4", "dev": true, "requires": {"accepts": "~1.3.5", "bytes": "3.0.0", "compressible": "~2.0.16", "debug": "2.6.9", "on-headers": "~1.0.2", "safe-buffer": "5.1.2", "vary": "~1.1.2"}, "dependencies": {"debug": {"version": "2.6.9", "dev": true, "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "dev": true}, "safe-buffer": {"version": "5.1.2", "dev": true}}}, "concat-map": {"version": "0.0.1", "dev": true}, "config-chain": {"version": "1.1.13", "dev": true, "optional": true, "requires": {"ini": "^1.3.4", "proto-list": "~1.2.1"}}, "connect-history-api-fallback": {"version": "1.6.0", "dev": true}, "content-disposition": {"version": "0.5.3", "dev": true, "requires": {"safe-buffer": "5.1.2"}, "dependencies": {"safe-buffer": {"version": "5.1.2", "dev": true}}}, "content-type": {"version": "1.0.4", "dev": true}, "cookie": {"version": "0.4.0", "dev": true}, "cookie-signature": {"version": "1.0.6", "dev": true}, "core-util-is": {"version": "1.0.3", "dev": true}, "cross-env": {"version": "7.0.3", "resolved": "https://registry.npmjs.org/cross-env/-/cross-env-7.0.3.tgz", "integrity": "sha512-+/HKd6EgcQCJGh2PSjZuUitQBQynKor4wrFbRg4DtAgS1aWO+gU52xpH7M9ScGgXSYmAVS9bIJ8EzuaGw0oNAw==", "requires": {"cross-spawn": "^7.0.1"}}, "cross-spawn": {"version": "7.0.3", "requires": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}}, "crypto-random-string": {"version": "2.0.0", "dev": true}, "css-loader": {"version": "6.2.0", "dev": true, "requires": {"icss-utils": "^5.1.0", "postcss": "^8.2.15", "postcss-modules-extract-imports": "^3.0.0", "postcss-modules-local-by-default": "^4.0.0", "postcss-modules-scope": "^3.0.0", "postcss-modules-values": "^4.0.0", "postcss-value-parser": "^4.1.0", "semver": "^7.3.5"}}, "css-select": {"version": "4.1.3", "dev": true, "requires": {"boolbase": "^1.0.0", "css-what": "^5.0.0", "domhandler": "^4.2.0", "domutils": "^2.6.0", "nth-check": "^2.0.0"}}, "css-tree": {"version": "1.1.3", "dev": true, "optional": true, "requires": {"mdn-data": "2.0.14", "source-map": "^0.6.1"}}, "css-what": {"version": "5.0.1", "dev": true}, "cssesc": {"version": "3.0.0", "dev": true}, "csso": {"version": "4.2.0", "dev": true, "optional": true, "requires": {"css-tree": "^1.1.2"}}, "cwebp-bin": {"version": "7.0.1", "dev": true, "optional": true, "requires": {"bin-build": "^3.0.0", "bin-wrapper": "^4.0.1"}}, "debug": {"version": "4.3.2", "requires": {"ms": "2.1.2"}}, "decamelize": {"version": "5.0.0", "dev": true}, "decamelize-keys": {"version": "1.1.0", "dev": true, "requires": {"decamelize": "^1.1.0", "map-obj": "^1.0.0"}, "dependencies": {"decamelize": {"version": "1.2.0", "dev": true}, "map-obj": {"version": "1.0.1", "dev": true}}}, "decode-uri-component": {"version": "0.2.0", "dev": true, "optional": true}, "decompress": {"version": "4.2.1", "dev": true, "optional": true, "requires": {"decompress-tar": "^4.0.0", "decompress-tarbz2": "^4.0.0", "decompress-targz": "^4.0.0", "decompress-unzip": "^4.0.1", "graceful-fs": "^4.1.10", "make-dir": "^1.0.0", "pify": "^2.3.0", "strip-dirs": "^2.0.0"}, "dependencies": {"make-dir": {"version": "1.3.0", "dev": true, "optional": true, "requires": {"pify": "^3.0.0"}, "dependencies": {"pify": {"version": "3.0.0", "dev": true, "optional": true}}}, "pify": {"version": "2.3.0", "dev": true, "optional": true}}}, "decompress-response": {"version": "3.3.0", "dev": true, "optional": true, "requires": {"mimic-response": "^1.0.0"}}, "decompress-tar": {"version": "4.1.1", "dev": true, "optional": true, "requires": {"file-type": "^5.2.0", "is-stream": "^1.1.0", "tar-stream": "^1.5.2"}, "dependencies": {"file-type": {"version": "5.2.0", "dev": true, "optional": true}, "is-stream": {"version": "1.1.0", "dev": true, "optional": true}}}, "decompress-tarbz2": {"version": "4.1.1", "dev": true, "optional": true, "requires": {"decompress-tar": "^4.1.0", "file-type": "^6.1.0", "is-stream": "^1.1.0", "seek-bzip": "^1.0.5", "unbzip2-stream": "^1.0.9"}, "dependencies": {"file-type": {"version": "6.2.0", "dev": true, "optional": true}, "is-stream": {"version": "1.1.0", "dev": true, "optional": true}}}, "decompress-targz": {"version": "4.1.1", "dev": true, "optional": true, "requires": {"decompress-tar": "^4.1.1", "file-type": "^5.2.0", "is-stream": "^1.1.0"}, "dependencies": {"file-type": {"version": "5.2.0", "dev": true, "optional": true}, "is-stream": {"version": "1.1.0", "dev": true, "optional": true}}}, "decompress-unzip": {"version": "4.0.1", "dev": true, "optional": true, "requires": {"file-type": "^3.8.0", "get-stream": "^2.2.0", "pify": "^2.3.0", "yauzl": "^2.4.2"}, "dependencies": {"file-type": {"version": "3.9.0", "dev": true, "optional": true}, "get-stream": {"version": "2.3.1", "dev": true, "optional": true, "requires": {"object-assign": "^4.0.1", "pinkie-promise": "^2.0.0"}}, "pify": {"version": "2.3.0", "dev": true, "optional": true}}}, "deep-equal": {"version": "1.1.1", "dev": true, "requires": {"is-arguments": "^1.0.4", "is-date-object": "^1.0.1", "is-regex": "^1.0.4", "object-is": "^1.0.1", "object-keys": "^1.1.1", "regexp.prototype.flags": "^1.2.0"}}, "default-gateway": {"version": "6.0.3", "dev": true, "requires": {"execa": "^5.0.0"}}, "define-lazy-prop": {"version": "2.0.0", "dev": true}, "define-properties": {"version": "1.1.3", "dev": true, "requires": {"object-keys": "^1.0.12"}}, "del": {"version": "6.0.0", "dev": true, "requires": {"globby": "^11.0.1", "graceful-fs": "^4.2.4", "is-glob": "^4.0.1", "is-path-cwd": "^2.2.0", "is-path-inside": "^3.0.2", "p-map": "^4.0.0", "rimraf": "^3.0.2", "slash": "^3.0.0"}}, "delayed-stream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ=="}, "depd": {"version": "1.1.2", "dev": true}, "destroy": {"version": "1.0.4", "dev": true}, "detect-node": {"version": "2.1.0", "dev": true}, "dir-glob": {"version": "3.0.1", "dev": true, "requires": {"path-type": "^4.0.0"}}, "dns-equal": {"version": "1.0.0", "dev": true}, "dns-packet": {"version": "1.3.4", "dev": true, "requires": {"ip": "^1.1.0", "safe-buffer": "^5.0.1"}}, "dns-txt": {"version": "2.0.2", "dev": true, "requires": {"buffer-indexof": "^1.0.0"}}, "dom-converter": {"version": "0.2.0", "dev": true, "requires": {"utila": "~0.4"}}, "dom-serializer": {"version": "1.3.2", "dev": true, "requires": {"domelementtype": "^2.0.1", "domhandler": "^4.2.0", "entities": "^2.0.0"}}, "dom-walk": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/dom-walk/-/dom-walk-0.1.2.tgz", "integrity": "sha512-6QvTW9mrGeIegrFXdtQi9pk7O/nSK6lSdXW2eqUspN5LWD7UTji2Fqw5V2YLjBpHEoU9Xl/eUWNpDeZvoyOv2w=="}, "domelementtype": {"version": "2.2.0", "dev": true}, "domhandler": {"version": "4.2.2", "dev": true, "requires": {"domelementtype": "^2.2.0"}}, "domutils": {"version": "2.8.0", "dev": true, "requires": {"dom-serializer": "^1.0.1", "domelementtype": "^2.2.0", "domhandler": "^4.2.0"}}, "dot-case": {"version": "3.0.4", "dev": true, "requires": {"no-case": "^3.0.4", "tslib": "^2.0.3"}}, "dotenv": {"version": "8.6.0", "resolved": "https://registry.npmjs.org/dotenv/-/dotenv-8.6.0.tgz", "integrity": "sha512-IrPdXQsk2BbzvCBGBOTmmSH5SodmqZNt4ERAZDmW4CT+tL8VtvinqywuANaFu4bOMWki16nqf0e4oC0QIaDr/g==", "dev": true}, "dotenv-defaults": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/dotenv-defaults/-/dotenv-defaults-2.0.2.tgz", "integrity": "sha512-iOIzovWfsUHU91L5i8bJce3NYK5JXeAwH50Jh6+ARUdLiiGlYWfGw6UkzsYqaXZH/hjE/eCd/PlfM/qqyK0AMg==", "dev": true, "requires": {"dotenv": "^8.2.0"}}, "dotenv-webpack": {"version": "8.1.0", "resolved": "https://registry.npmjs.org/dotenv-webpack/-/dotenv-webpack-8.1.0.tgz", "integrity": "sha512-owK1JcsPkIobeqjVrk6h7jPED/W6ZpdFsMPR+5ursB7/SdgDyO+VzAU+szK8C8u3qUhtENyYnj8eyXMR5kkGag==", "dev": true, "requires": {"dotenv-defaults": "^2.0.2"}}, "download": {"version": "6.2.5", "dev": true, "optional": true, "requires": {"caw": "^2.0.0", "content-disposition": "^0.5.2", "decompress": "^4.0.0", "ext-name": "^5.0.0", "file-type": "5.2.0", "filenamify": "^2.0.0", "get-stream": "^3.0.0", "got": "^7.0.0", "make-dir": "^1.0.0", "p-event": "^1.0.0", "pify": "^3.0.0"}, "dependencies": {"file-type": {"version": "5.2.0", "dev": true, "optional": true}, "get-stream": {"version": "3.0.0", "dev": true, "optional": true}, "make-dir": {"version": "1.3.0", "dev": true, "optional": true, "requires": {"pify": "^3.0.0"}}, "pify": {"version": "3.0.0", "dev": true, "optional": true}}}, "duplexer3": {"version": "0.1.5", "dev": true, "optional": true}, "ee-first": {"version": "1.1.1", "dev": true}, "electron-to-chromium": {"version": "1.3.828"}, "emojis-list": {"version": "3.0.0", "dev": true}, "encodeurl": {"version": "1.0.2", "dev": true}, "end-of-stream": {"version": "1.4.4", "dev": true, "optional": true, "requires": {"once": "^1.4.0"}}, "enhanced-resolve": {"version": "5.8.2", "dev": true, "requires": {"graceful-fs": "^4.2.4", "tapable": "^2.2.0"}}, "entities": {"version": "2.2.0", "dev": true}, "envinfo": {"version": "7.10.0", "resolved": "https://registry.npmjs.org/envinfo/-/envinfo-7.10.0.tgz", "integrity": "sha512-ZtUjZO6l5mwTHvc1L9+1q5p/R3wTopcfqMW8r5t8SJSKqeVI/LtajORwRFEKpEFuekjD0VBjwu1HMxL4UalIRw==", "dev": true}, "error-ex": {"version": "1.3.2", "dev": true, "requires": {"is-arrayish": "^0.2.1"}}, "es-module-lexer": {"version": "0.7.1", "dev": true}, "escalade": {"version": "3.1.1"}, "escape-html": {"version": "1.0.3", "dev": true}, "escape-string-regexp": {"version": "1.0.5", "dev": true}, "eslint-scope": {"version": "5.1.1", "dev": true, "requires": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}}, "esrecurse": {"version": "4.3.0", "dev": true, "requires": {"estraverse": "^5.2.0"}, "dependencies": {"estraverse": {"version": "5.2.0", "dev": true}}}, "estraverse": {"version": "4.3.0", "dev": true}, "etag": {"version": "1.8.1", "dev": true}, "eventemitter3": {"version": "4.0.7", "dev": true}, "events": {"version": "3.3.0", "dev": true}, "exec-buffer": {"version": "3.2.0", "dev": true, "optional": true, "requires": {"execa": "^0.7.0", "p-finally": "^1.0.0", "pify": "^3.0.0", "rimraf": "^2.5.4", "tempfile": "^2.0.0"}, "dependencies": {"cross-spawn": {"version": "5.1.0", "dev": true, "optional": true, "requires": {"lru-cache": "^4.0.1", "shebang-command": "^1.2.0", "which": "^1.2.9"}}, "execa": {"version": "0.7.0", "dev": true, "optional": true, "requires": {"cross-spawn": "^5.0.1", "get-stream": "^3.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}}, "get-stream": {"version": "3.0.0", "dev": true, "optional": true}, "is-stream": {"version": "1.1.0", "dev": true, "optional": true}, "lru-cache": {"version": "4.1.5", "dev": true, "optional": true, "requires": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "npm-run-path": {"version": "2.0.2", "dev": true, "optional": true, "requires": {"path-key": "^2.0.0"}}, "path-key": {"version": "2.0.1", "dev": true, "optional": true}, "pify": {"version": "3.0.0", "dev": true, "optional": true}, "rimraf": {"version": "2.7.1", "dev": true, "optional": true, "requires": {"glob": "^7.1.3"}}, "shebang-command": {"version": "1.2.0", "dev": true, "optional": true, "requires": {"shebang-regex": "^1.0.0"}}, "shebang-regex": {"version": "1.0.0", "dev": true, "optional": true}, "which": {"version": "1.3.1", "dev": true, "optional": true, "requires": {"isexe": "^2.0.0"}}, "yallist": {"version": "2.1.2", "dev": true, "optional": true}}}, "execa": {"version": "5.1.1", "dev": true, "requires": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}}, "executable": {"version": "4.1.1", "dev": true, "optional": true, "requires": {"pify": "^2.2.0"}, "dependencies": {"pify": {"version": "2.3.0", "dev": true, "optional": true}}}, "express": {"version": "4.17.1", "dev": true, "requires": {"accepts": "~1.3.7", "array-flatten": "1.1.1", "body-parser": "1.19.0", "content-disposition": "0.5.3", "content-type": "~1.0.4", "cookie": "0.4.0", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "~1.1.2", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "~1.1.2", "fresh": "0.5.2", "merge-descriptors": "1.0.1", "methods": "~1.1.2", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "path-to-regexp": "0.1.7", "proxy-addr": "~2.0.5", "qs": "6.7.0", "range-parser": "~1.2.1", "safe-buffer": "5.1.2", "send": "0.17.1", "serve-static": "1.14.1", "setprototypeof": "1.1.1", "statuses": "~1.5.0", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "dependencies": {"array-flatten": {"version": "1.1.1", "dev": true}, "debug": {"version": "2.6.9", "dev": true, "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "dev": true}, "safe-buffer": {"version": "5.1.2", "dev": true}}}, "ext-list": {"version": "2.2.2", "dev": true, "optional": true, "requires": {"mime-db": "^1.28.0"}}, "ext-name": {"version": "5.0.0", "dev": true, "optional": true, "requires": {"ext-list": "^2.0.0", "sort-keys-length": "^1.0.0"}}, "fast-deep-equal": {"version": "3.1.3", "dev": true}, "fast-glob": {"version": "3.2.7", "dev": true, "requires": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.4"}}, "fast-json-stable-stringify": {"version": "2.1.0", "dev": true}, "fast-xml-parser": {"version": "3.21.1", "dev": true, "optional": true, "requires": {"strnum": "^1.0.4"}}, "fastest-levenshtein": {"version": "1.0.12", "dev": true}, "fastq": {"version": "1.12.0", "dev": true, "requires": {"reusify": "^1.0.4"}}, "faye-websocket": {"version": "0.11.4", "dev": true, "requires": {"websocket-driver": ">=0.5.1"}}, "fd-slicer": {"version": "1.1.0", "dev": true, "optional": true, "requires": {"pend": "~1.2.0"}}, "fetch-blob": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-3.2.0.tgz", "integrity": "sha512-7yAQpD2UMJzLi1Dqv7qFYnPbaPx7ZfFK6PiIxQ4PfkGPyNyl2Ugx+a/umUonmKqjhM4DnfbMvdX6otXq83soQQ==", "requires": {"node-domexception": "^1.0.0", "web-streams-polyfill": "^3.0.3"}}, "file-type": {"version": "16.5.3", "dev": true, "requires": {"readable-web-to-node-stream": "^3.0.0", "strtok3": "^6.2.4", "token-types": "^4.1.1"}}, "filename-reserved-regex": {"version": "2.0.0", "dev": true, "optional": true}, "filenamify": {"version": "2.1.0", "dev": true, "optional": true, "requires": {"filename-reserved-regex": "^2.0.0", "strip-outer": "^1.0.0", "trim-repeated": "^1.0.0"}}, "fill-range": {"version": "7.0.1", "dev": true, "requires": {"to-regex-range": "^5.0.1"}}, "finalhandler": {"version": "1.1.2", "dev": true, "requires": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "statuses": "~1.5.0", "unpipe": "~1.0.0"}, "dependencies": {"debug": {"version": "2.6.9", "dev": true, "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "dev": true}}}, "find-up": {"version": "5.0.0", "dev": true, "requires": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}}, "find-versions": {"version": "3.2.0", "dev": true, "optional": true, "requires": {"semver-regex": "^2.0.0"}}, "follow-redirects": {"version": "1.15.6", "resolved": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.6.tgz", "integrity": "sha512-wWN62YITEaOpSK584EZXJafH1AGpO8RVgElfkuXbTOrPX4fIfOyEpW/CsiNd8JdYrAoOvafRTOEnvsO++qCqFA=="}, "form-data": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/form-data/-/form-data-4.0.0.tgz", "integrity": "sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==", "requires": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}}, "formdata-polyfill": {"version": "4.0.10", "resolved": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-4.0.10.tgz", "integrity": "sha512-buewHzMvYL29jdeQTVILecSaZKnt/RJWjoZCF5OW60Z67/GmSLBkOFM7qh1PI3zFNtJbaZL5eQu1vLfazOwj4g==", "requires": {"fetch-blob": "^3.1.2"}}, "forwarded": {"version": "0.2.0", "dev": true}, "fresh": {"version": "0.5.2", "dev": true}, "from2": {"version": "2.3.0", "dev": true, "optional": true, "requires": {"inherits": "^2.0.1", "readable-stream": "^2.0.0"}, "dependencies": {"readable-stream": {"version": "2.3.7", "dev": true, "optional": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "safe-buffer": {"version": "5.1.2", "dev": true, "optional": true}, "string_decoder": {"version": "1.1.1", "dev": true, "optional": true, "requires": {"safe-buffer": "~5.1.0"}}}}, "fs-constants": {"version": "1.0.0", "dev": true, "optional": true}, "fs-monkey": {"version": "1.0.3", "dev": true}, "fs.realpath": {"version": "1.0.0", "dev": true}, "function-bind": {"version": "1.1.1", "dev": true}, "get-float-time-domain-data": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/get-float-time-domain-data/-/get-float-time-domain-data-0.1.0.tgz", "integrity": "sha512-6lWdVMny7vJ+xWxGUmVdSW7rd4L2byptiVkjH3QEBsxcHSa8UbC/HsZmixHK/ZQ1EYsOaQjaeRgR+NAsIerEJQ=="}, "get-intrinsic": {"version": "1.1.1", "dev": true, "requires": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.1"}}, "get-proxy": {"version": "2.1.0", "dev": true, "optional": true, "requires": {"npm-conf": "^1.1.0"}}, "get-stdin": {"version": "9.0.0", "dev": true}, "get-stream": {"version": "6.0.1", "dev": true}, "gifsicle": {"version": "5.3.0", "dev": true, "optional": true, "requires": {"bin-build": "^3.0.0", "bin-wrapper": "^4.0.0", "execa": "^5.0.0"}}, "glob": {"version": "7.1.7", "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "glob-parent": {"version": "5.1.2", "dev": true, "requires": {"is-glob": "^4.0.1"}}, "glob-to-regexp": {"version": "0.4.1", "dev": true}, "global": {"version": "4.4.0", "resolved": "https://registry.npmjs.org/global/-/global-4.4.0.tgz", "integrity": "sha512-wv/LAoHdRE3BeTGz53FAamhGlPLhlssK45usmGFThIi4XqnBmjKQ16u+RNbP7WvigRZDxUsM0J3gcQ5yicaL0w==", "requires": {"min-document": "^2.19.0", "process": "^0.11.10"}}, "globby": {"version": "11.0.4", "dev": true, "requires": {"array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.1.1", "ignore": "^5.1.4", "merge2": "^1.3.0", "slash": "^3.0.0"}}, "got": {"version": "7.1.0", "dev": true, "optional": true, "requires": {"decompress-response": "^3.2.0", "duplexer3": "^0.1.4", "get-stream": "^3.0.0", "is-plain-obj": "^1.1.0", "is-retry-allowed": "^1.0.0", "is-stream": "^1.0.0", "isurl": "^1.0.0-alpha5", "lowercase-keys": "^1.0.0", "p-cancelable": "^0.3.0", "p-timeout": "^1.1.1", "safe-buffer": "^5.0.1", "timed-out": "^4.0.0", "url-parse-lax": "^1.0.0", "url-to-options": "^1.0.1"}, "dependencies": {"get-stream": {"version": "3.0.0", "dev": true, "optional": true}, "is-stream": {"version": "1.1.0", "dev": true, "optional": true}}}, "graceful-fs": {"version": "4.2.8", "dev": true}, "handle-thing": {"version": "2.0.1", "dev": true}, "hard-rejection": {"version": "2.1.0", "dev": true}, "has": {"version": "1.0.3", "dev": true, "requires": {"function-bind": "^1.1.1"}}, "has-flag": {"version": "3.0.0", "dev": true}, "has-symbol-support-x": {"version": "1.4.2", "dev": true, "optional": true}, "has-symbols": {"version": "1.0.2", "dev": true}, "has-to-string-tag-x": {"version": "1.4.1", "dev": true, "optional": true, "requires": {"has-symbol-support-x": "^1.4.1"}}, "has-tostringtag": {"version": "1.0.0", "dev": true, "requires": {"has-symbols": "^1.0.2"}}, "he": {"version": "1.2.0", "dev": true}, "hosted-git-info": {"version": "4.0.2", "dev": true, "requires": {"lru-cache": "^6.0.0"}}, "hpack.js": {"version": "2.1.6", "dev": true, "requires": {"inherits": "^2.0.1", "obuf": "^1.0.0", "readable-stream": "^2.0.1", "wbuf": "^1.1.0"}, "dependencies": {"readable-stream": {"version": "2.3.7", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "safe-buffer": {"version": "5.1.2", "dev": true}, "string_decoder": {"version": "1.1.1", "dev": true, "requires": {"safe-buffer": "~5.1.0"}}}}, "html-entities": {"version": "2.3.2", "dev": true}, "html-loader": {"version": "4.1.0", "dev": true, "requires": {"html-minifier-terser": "^6.1.0", "parse5": "^7.0.0"}, "dependencies": {"clean-css": {"version": "5.3.1", "dev": true, "requires": {"source-map": "~0.6.0"}}, "commander": {"version": "8.3.0", "dev": true}, "html-minifier-terser": {"version": "6.1.0", "dev": true, "requires": {"camel-case": "^4.1.2", "clean-css": "^5.2.2", "commander": "^8.3.0", "he": "^1.2.0", "param-case": "^3.0.4", "relateurl": "^0.2.7", "terser": "^5.10.0"}}, "terser": {"version": "5.14.2", "dev": true, "requires": {"@jridgewell/source-map": "^0.3.2", "acorn": "^8.5.0", "commander": "^2.20.0", "source-map-support": "~0.5.20"}, "dependencies": {"commander": {"version": "2.20.3", "dev": true}}}}}, "html-minifier-terser": {"version": "5.1.1", "dev": true, "requires": {"camel-case": "^4.1.1", "clean-css": "^4.2.3", "commander": "^4.1.1", "he": "^1.2.0", "param-case": "^3.0.3", "relateurl": "^0.2.7", "terser": "^4.6.3"}}, "html-webpack-plugin": {"version": "5.3.2", "dev": true, "requires": {"@types/html-minifier-terser": "^5.0.0", "html-minifier-terser": "^5.0.1", "lodash": "^4.17.21", "pretty-error": "^3.0.4", "tapable": "^2.0.0"}}, "htmlparser2": {"version": "6.1.0", "dev": true, "requires": {"domelementtype": "^2.0.1", "domhandler": "^4.0.0", "domutils": "^2.5.2", "entities": "^2.0.0"}}, "http-cache-semantics": {"version": "3.8.1", "dev": true, "optional": true}, "http-deceiver": {"version": "1.2.7", "dev": true}, "http-errors": {"version": "1.7.2", "dev": true, "requires": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.1", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0"}, "dependencies": {"inherits": {"version": "2.0.3", "dev": true}}}, "http-parser-js": {"version": "0.5.3", "dev": true}, "http-proxy": {"version": "1.18.1", "dev": true, "requires": {"eventemitter3": "^4.0.0", "follow-redirects": "^1.0.0", "requires-port": "^1.0.0"}}, "http-proxy-middleware": {"version": "2.0.4", "dev": true, "requires": {"@types/http-proxy": "^1.17.8", "http-proxy": "^1.18.1", "is-glob": "^4.0.1", "is-plain-obj": "^3.0.0", "micromatch": "^4.0.2"}, "dependencies": {"is-plain-obj": {"version": "3.0.0", "dev": true}}}, "human-signals": {"version": "2.1.0", "dev": true}, "iconv-lite": {"version": "0.4.24", "dev": true, "requires": {"safer-buffer": ">= 2.1.2 < 3"}}, "icss-utils": {"version": "5.1.0", "dev": true, "requires": {}}, "ieee754": {"version": "1.2.1", "dev": true}, "ignore": {"version": "5.1.8", "dev": true}, "image-webpack-loader": {"version": "8.1.0", "dev": true, "requires": {"imagemin": "^7.0.1", "imagemin-gifsicle": "^7.0.0", "imagemin-mozjpeg": "^9.0.0", "imagemin-optipng": "^8.0.0", "imagemin-pngquant": "^9.0.2", "imagemin-svgo": "^9.0.0", "imagemin-webp": "^7.0.0", "loader-utils": "^2.0.0", "object-assign": "^4.1.1", "schema-utils": "^2.7.1"}, "dependencies": {"schema-utils": {"version": "2.7.1", "dev": true, "requires": {"@types/json-schema": "^7.0.5", "ajv": "^6.12.4", "ajv-keywords": "^3.5.2"}}}}, "imagemin": {"version": "7.0.1", "dev": true, "requires": {"file-type": "^12.0.0", "globby": "^10.0.0", "graceful-fs": "^4.2.2", "junk": "^3.1.0", "make-dir": "^3.0.0", "p-pipe": "^3.0.0", "replace-ext": "^1.0.0"}, "dependencies": {"file-type": {"version": "12.4.2", "dev": true}, "globby": {"version": "10.0.2", "dev": true, "requires": {"@types/glob": "^7.1.1", "array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.0.3", "glob": "^7.1.3", "ignore": "^5.1.1", "merge2": "^1.2.3", "slash": "^3.0.0"}}}}, "imagemin-gifsicle": {"version": "7.0.0", "dev": true, "optional": true, "requires": {"execa": "^1.0.0", "gifsicle": "^5.0.0", "is-gif": "^3.0.0"}, "dependencies": {"cross-spawn": {"version": "6.0.5", "dev": true, "optional": true, "requires": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}}, "execa": {"version": "1.0.0", "dev": true, "optional": true, "requires": {"cross-spawn": "^6.0.0", "get-stream": "^4.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}}, "get-stream": {"version": "4.1.0", "dev": true, "optional": true, "requires": {"pump": "^3.0.0"}}, "is-stream": {"version": "1.1.0", "dev": true, "optional": true}, "npm-run-path": {"version": "2.0.2", "dev": true, "optional": true, "requires": {"path-key": "^2.0.0"}}, "path-key": {"version": "2.0.1", "dev": true, "optional": true}, "semver": {"version": "5.7.1", "dev": true, "optional": true}, "shebang-command": {"version": "1.2.0", "dev": true, "optional": true, "requires": {"shebang-regex": "^1.0.0"}}, "shebang-regex": {"version": "1.0.0", "dev": true, "optional": true}, "which": {"version": "1.3.1", "dev": true, "optional": true, "requires": {"isexe": "^2.0.0"}}}}, "imagemin-mozjpeg": {"version": "9.0.0", "dev": true, "optional": true, "requires": {"execa": "^4.0.0", "is-jpg": "^2.0.0", "mozjpeg": "^7.0.0"}, "dependencies": {"execa": {"version": "4.1.0", "dev": true, "optional": true, "requires": {"cross-spawn": "^7.0.0", "get-stream": "^5.0.0", "human-signals": "^1.1.1", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "onetime": "^5.1.0", "signal-exit": "^3.0.2", "strip-final-newline": "^2.0.0"}}, "get-stream": {"version": "5.2.0", "dev": true, "optional": true, "requires": {"pump": "^3.0.0"}}, "human-signals": {"version": "1.1.1", "dev": true, "optional": true}}}, "imagemin-optipng": {"version": "8.0.0", "dev": true, "optional": true, "requires": {"exec-buffer": "^3.0.0", "is-png": "^2.0.0", "optipng-bin": "^7.0.0"}}, "imagemin-pngquant": {"version": "9.0.2", "dev": true, "optional": true, "requires": {"execa": "^4.0.0", "is-png": "^2.0.0", "is-stream": "^2.0.0", "ow": "^0.17.0", "pngquant-bin": "^6.0.0"}, "dependencies": {"execa": {"version": "4.1.0", "dev": true, "optional": true, "requires": {"cross-spawn": "^7.0.0", "get-stream": "^5.0.0", "human-signals": "^1.1.1", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "onetime": "^5.1.0", "signal-exit": "^3.0.2", "strip-final-newline": "^2.0.0"}}, "get-stream": {"version": "5.2.0", "dev": true, "optional": true, "requires": {"pump": "^3.0.0"}}, "human-signals": {"version": "1.1.1", "dev": true, "optional": true}}}, "imagemin-svgo": {"version": "9.0.0", "dev": true, "optional": true, "requires": {"is-svg": "^4.2.1", "svgo": "^2.1.0"}}, "imagemin-webp": {"version": "7.0.0", "dev": true, "optional": true, "requires": {"cwebp-bin": "^7.0.1", "exec-buffer": "^3.2.0", "is-cwebp-readable": "^3.0.0"}}, "import-lazy": {"version": "3.1.0", "dev": true, "optional": true}, "import-local": {"version": "3.0.2", "dev": true, "requires": {"pkg-dir": "^4.2.0", "resolve-cwd": "^3.0.0"}}, "indent-string": {"version": "5.0.0", "dev": true}, "inflight": {"version": "1.0.6", "dev": true, "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.4", "dev": true}, "ini": {"version": "1.3.8", "dev": true, "optional": true}, "interpret": {"version": "2.2.0", "dev": true}, "into-stream": {"version": "3.1.0", "dev": true, "optional": true, "requires": {"from2": "^2.1.1", "p-is-promise": "^1.1.0"}}, "ip": {"version": "1.1.5", "dev": true}, "ipaddr.js": {"version": "1.9.1", "dev": true}, "is-arguments": {"version": "1.1.1", "dev": true, "requires": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}}, "is-arrayish": {"version": "0.2.1", "dev": true}, "is-binary-path": {"version": "2.1.0", "dev": true, "requires": {"binary-extensions": "^2.0.0"}}, "is-core-module": {"version": "2.6.0", "dev": true, "requires": {"has": "^1.0.3"}}, "is-cwebp-readable": {"version": "3.0.0", "dev": true, "optional": true, "requires": {"file-type": "^10.5.0"}, "dependencies": {"file-type": {"version": "10.11.0", "dev": true, "optional": true}}}, "is-date-object": {"version": "1.0.5", "dev": true, "requires": {"has-tostringtag": "^1.0.0"}}, "is-docker": {"version": "2.2.1", "dev": true}, "is-extglob": {"version": "2.1.1", "dev": true}, "is-gif": {"version": "3.0.0", "dev": true, "optional": true, "requires": {"file-type": "^10.4.0"}, "dependencies": {"file-type": {"version": "10.11.0", "dev": true, "optional": true}}}, "is-glob": {"version": "4.0.1", "dev": true, "requires": {"is-extglob": "^2.1.1"}}, "is-jpg": {"version": "2.0.0", "dev": true, "optional": true}, "is-natural-number": {"version": "4.0.1", "dev": true, "optional": true}, "is-number": {"version": "7.0.0", "dev": true}, "is-object": {"version": "1.0.2", "dev": true, "optional": true}, "is-path-cwd": {"version": "2.2.0", "dev": true}, "is-path-inside": {"version": "3.0.3", "dev": true}, "is-plain-obj": {"version": "1.1.0", "dev": true}, "is-plain-object": {"version": "2.0.4", "dev": true, "requires": {"isobject": "^3.0.1"}}, "is-png": {"version": "2.0.0", "dev": true, "optional": true}, "is-regex": {"version": "1.1.4", "dev": true, "requires": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}}, "is-retry-allowed": {"version": "1.2.0", "dev": true, "optional": true}, "is-stream": {"version": "2.0.1", "dev": true}, "is-svg": {"version": "4.3.2", "dev": true, "optional": true, "requires": {"fast-xml-parser": "^3.19.0"}}, "is-wsl": {"version": "2.2.0", "dev": true, "requires": {"is-docker": "^2.0.0"}}, "isarray": {"version": "1.0.0", "dev": true}, "isexe": {"version": "2.0.0"}, "isobject": {"version": "3.0.1", "dev": true}, "isurl": {"version": "1.0.0", "dev": true, "optional": true, "requires": {"has-to-string-tag-x": "^1.2.0", "is-object": "^1.0.1"}}, "jest-worker": {"version": "27.1.0", "dev": true, "requires": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "dependencies": {"has-flag": {"version": "4.0.0", "dev": true}, "supports-color": {"version": "8.1.1", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "js-tokens": {"version": "4.0.0", "dev": true}, "json-buffer": {"version": "3.0.0", "dev": true, "optional": true}, "json-parse-better-errors": {"version": "1.0.2", "dev": true}, "json-parse-even-better-errors": {"version": "2.3.1", "dev": true}, "json-schema-traverse": {"version": "0.4.1", "dev": true}, "json5": {"version": "2.2.1", "dev": true}, "junk": {"version": "3.1.0", "dev": true}, "jwt-decode": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/jwt-decode/-/jwt-decode-3.1.2.tgz", "integrity": "sha512-UfpWE/VZn0iP50d8cz9NrZLM9lSWhcJ+0Gt/nm4by88UL+J1SiKN8/5dkjMmbEzwL2CAe+67GsegCbIKtbp75A=="}, "keyv": {"version": "3.0.0", "dev": true, "optional": true, "requires": {"json-buffer": "3.0.0"}}, "kind-of": {"version": "6.0.3", "dev": true}, "lines-and-columns": {"version": "1.1.6", "dev": true}, "loader-runner": {"version": "4.2.0", "dev": true}, "loader-utils": {"version": "2.0.2", "dev": true, "requires": {"big.js": "^5.2.2", "emojis-list": "^3.0.0", "json5": "^2.1.2"}}, "locate-path": {"version": "6.0.0", "dev": true, "requires": {"p-locate": "^5.0.0"}}, "lodash": {"version": "4.17.21", "dev": true}, "lodash.isequal": {"version": "4.5.0", "resolved": "https://registry.npmjs.org/lodash.isequal/-/lodash.isequal-4.5.0.tgz", "integrity": "sha512-pDo3lu8Jhfjqls6GkMgpahsF9kCyayhgykjyLMNFTKWrpVdAQtYyB4muAMWozBB4ig/dtWAmsMxLEI8wuz+DYQ=="}, "lodash.merge": {"version": "4.6.2", "resolved": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz", "integrity": "sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ=="}, "lower-case": {"version": "2.0.2", "dev": true, "requires": {"tslib": "^2.0.3"}}, "lowercase-keys": {"version": "1.0.1", "dev": true, "optional": true}, "lru-cache": {"version": "6.0.0", "dev": true, "requires": {"yallist": "^4.0.0"}}, "make-dir": {"version": "3.1.0", "dev": true, "requires": {"semver": "^6.0.0"}, "dependencies": {"semver": {"version": "6.3.0", "dev": true}}}, "map-obj": {"version": "4.2.1", "dev": true}, "mdn-data": {"version": "2.0.14", "dev": true, "optional": true}, "media-typer": {"version": "0.3.0", "dev": true}, "memfs": {"version": "3.4.1", "dev": true, "requires": {"fs-monkey": "1.0.3"}}, "meow": {"version": "10.1.1", "dev": true, "requires": {"@types/minimist": "^1.2.2", "camelcase-keys": "^7.0.0", "decamelize": "^5.0.0", "decamelize-keys": "^1.1.0", "hard-rejection": "^2.1.0", "minimist-options": "4.1.0", "normalize-package-data": "^3.0.2", "read-pkg-up": "^8.0.0", "redent": "^4.0.0", "trim-newlines": "^4.0.2", "type-fest": "^1.2.2", "yargs-parser": "^20.2.9"}}, "merge-descriptors": {"version": "1.0.1", "dev": true}, "merge-stream": {"version": "2.0.0", "dev": true}, "merge2": {"version": "1.4.1", "dev": true}, "methods": {"version": "1.1.2", "dev": true}, "micromatch": {"version": "4.0.4", "dev": true, "requires": {"braces": "^3.0.1", "picomatch": "^2.2.3"}}, "mime": {"version": "1.6.0", "dev": true}, "mime-db": {"version": "1.49.0"}, "mime-types": {"version": "2.1.32", "requires": {"mime-db": "1.49.0"}}, "mimic-fn": {"version": "2.1.0", "dev": true}, "mimic-response": {"version": "1.0.1", "dev": true, "optional": true}, "min-document": {"version": "2.19.0", "resolved": "https://registry.npmjs.org/min-document/-/min-document-2.19.0.tgz", "integrity": "sha512-9Wy1B3m3f66bPPmU5hdA4DR4PB2OfDU/+GS3yAB7IQozE3tqXaVv2zOjgla7MEGSRv95+ILmOuvhLkOK6wJtCQ==", "requires": {"dom-walk": "^0.1.0"}}, "min-indent": {"version": "1.0.1", "dev": true}, "mini-css-extract-plugin": {"version": "2.2.2", "dev": true, "requires": {"schema-utils": "^3.1.0"}}, "minimalistic-assert": {"version": "1.0.1", "dev": true}, "minimatch": {"version": "3.0.4", "dev": true, "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "1.2.6", "dev": true}, "minimist-options": {"version": "4.1.0", "dev": true, "requires": {"arrify": "^1.0.1", "is-plain-obj": "^1.1.0", "kind-of": "^6.0.3"}}, "mkdirp": {"version": "0.5.5", "dev": true, "requires": {"minimist": "^1.2.5"}}, "moment": {"version": "2.29.4", "resolved": "https://registry.npmjs.org/moment/-/moment-2.29.4.tgz", "integrity": "sha512-5LC9SOxjSc2HF6vO2CyuTDNivEdoz2IvyJJGj6X8DJ0eFyfszE0QiEd+iXmBvUP3WHxSjFH/vIsA0EN00cgr8w=="}, "mozjpeg": {"version": "7.1.1", "dev": true, "optional": true, "requires": {"bin-build": "^3.0.0", "bin-wrapper": "^4.0.0"}}, "ms": {"version": "2.1.2"}, "multicast-dns": {"version": "6.2.3", "dev": true, "requires": {"dns-packet": "^1.3.1", "thunky": "^1.0.2"}}, "multicast-dns-service-types": {"version": "1.1.0", "dev": true}, "nanoid": {"version": "3.3.1", "dev": true}, "negotiator": {"version": "0.6.2", "dev": true}, "neo-async": {"version": "2.6.2", "dev": true}, "nice-try": {"version": "1.0.5", "dev": true, "optional": true}, "nipplejs": {"version": "0.10.1", "resolved": "https://registry.npmjs.org/nipplejs/-/nipplejs-0.10.1.tgz", "integrity": "sha512-BuKBDfdd7BVK6E7sivHwrRPh9TETsHuHEwuT95nAjRz2uJu5roYngNs+BdRe8nYf8mP6OZ9aRqdgMlqVsDMRcw=="}, "no-case": {"version": "3.0.4", "dev": true, "requires": {"lower-case": "^2.0.2", "tslib": "^2.0.3"}}, "node-domexception": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/node-domexception/-/node-domexception-1.0.0.tgz", "integrity": "sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ=="}, "node-forge": {"version": "1.3.0", "dev": true}, "node-releases": {"version": "1.1.75"}, "normalize-package-data": {"version": "3.0.3", "dev": true, "requires": {"hosted-git-info": "^4.0.1", "is-core-module": "^2.5.0", "semver": "^7.3.4", "validate-npm-package-license": "^3.0.1"}}, "normalize-path": {"version": "3.0.0", "dev": true}, "normalize-url": {"version": "2.0.1", "dev": true, "optional": true, "requires": {"prepend-http": "^2.0.0", "query-string": "^5.0.1", "sort-keys": "^2.0.0"}, "dependencies": {"prepend-http": {"version": "2.0.0", "dev": true, "optional": true}, "sort-keys": {"version": "2.0.0", "dev": true, "optional": true, "requires": {"is-plain-obj": "^1.0.0"}}}}, "npm-conf": {"version": "1.1.3", "dev": true, "optional": true, "requires": {"config-chain": "^1.1.11", "pify": "^3.0.0"}, "dependencies": {"pify": {"version": "3.0.0", "dev": true, "optional": true}}}, "npm-run-path": {"version": "4.0.1", "dev": true, "requires": {"path-key": "^3.0.0"}}, "nth-check": {"version": "2.0.1", "dev": true, "requires": {"boolbase": "^1.0.0"}}, "object-assign": {"version": "4.1.1", "dev": true}, "object-is": {"version": "1.1.5", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.3"}}, "object-keys": {"version": "1.1.1", "dev": true}, "obuf": {"version": "1.1.2", "dev": true}, "on-finished": {"version": "2.3.0", "dev": true, "requires": {"ee-first": "1.1.1"}}, "on-headers": {"version": "1.0.2", "dev": true}, "once": {"version": "1.4.0", "dev": true, "requires": {"wrappy": "1"}}, "onetime": {"version": "5.1.2", "dev": true, "requires": {"mimic-fn": "^2.1.0"}}, "open": {"version": "8.2.1", "dev": true, "requires": {"define-lazy-prop": "^2.0.0", "is-docker": "^2.1.1", "is-wsl": "^2.2.0"}}, "open-cli": {"version": "7.0.1", "dev": true, "requires": {"file-type": "^16.5.0", "get-stdin": "^9.0.0", "meow": "^10.0.1", "open": "^8.2.0", "tempy": "^1.0.1"}}, "optipng-bin": {"version": "7.0.1", "dev": true, "optional": true, "requires": {"bin-build": "^3.0.0", "bin-wrapper": "^4.0.0"}}, "os-filter-obj": {"version": "2.0.0", "dev": true, "optional": true, "requires": {"arch": "^2.1.0"}}, "ow": {"version": "0.17.0", "dev": true, "optional": true, "requires": {"type-fest": "^0.11.0"}, "dependencies": {"type-fest": {"version": "0.11.0", "dev": true, "optional": true}}}, "p-cancelable": {"version": "0.3.0", "dev": true, "optional": true}, "p-event": {"version": "1.3.0", "dev": true, "optional": true, "requires": {"p-timeout": "^1.1.1"}}, "p-finally": {"version": "1.0.0", "dev": true, "optional": true}, "p-is-promise": {"version": "1.1.0", "dev": true, "optional": true}, "p-limit": {"version": "3.1.0", "dev": true, "requires": {"yocto-queue": "^0.1.0"}}, "p-locate": {"version": "5.0.0", "dev": true, "requires": {"p-limit": "^3.0.2"}}, "p-map": {"version": "4.0.0", "dev": true, "requires": {"aggregate-error": "^3.0.0"}}, "p-map-series": {"version": "1.0.0", "dev": true, "optional": true, "requires": {"p-reduce": "^1.0.0"}}, "p-pipe": {"version": "3.1.0", "dev": true}, "p-reduce": {"version": "1.0.0", "dev": true, "optional": true}, "p-retry": {"version": "4.6.1", "dev": true, "requires": {"@types/retry": "^0.12.0", "retry": "^0.13.1"}}, "p-timeout": {"version": "1.2.1", "dev": true, "optional": true, "requires": {"p-finally": "^1.0.0"}}, "p-try": {"version": "2.2.0", "dev": true}, "pako": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/pako/-/pako-2.1.0.tgz", "integrity": "sha512-w+eufiZ1WuJYgPXbV/PO3NCMEc3xqylkKHzp8bxp1uW4qaSNQUkwmLLEc3kKsfz8lpV1F8Ht3U1Cm+9Srog2ug=="}, "param-case": {"version": "3.0.4", "dev": true, "requires": {"dot-case": "^3.0.4", "tslib": "^2.0.3"}}, "parse-json": {"version": "5.2.0", "dev": true, "requires": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}}, "parse5": {"version": "7.0.0", "dev": true, "requires": {"entities": "^4.3.0"}, "dependencies": {"entities": {"version": "4.3.1", "dev": true}}}, "parseurl": {"version": "1.3.3", "dev": true}, "pascal-case": {"version": "3.1.2", "dev": true, "requires": {"no-case": "^3.0.4", "tslib": "^2.0.3"}}, "path-exists": {"version": "4.0.0", "dev": true}, "path-is-absolute": {"version": "1.0.1", "dev": true}, "path-key": {"version": "3.1.1"}, "path-parse": {"version": "1.0.7", "dev": true}, "path-to-regexp": {"version": "0.1.7", "dev": true}, "path-type": {"version": "4.0.0", "dev": true}, "peek-readable": {"version": "4.0.1", "dev": true}, "pend": {"version": "1.2.0", "dev": true, "optional": true}, "picocolors": {"version": "1.0.0", "dev": true, "optional": true}, "picomatch": {"version": "2.3.0", "dev": true}, "pify": {"version": "4.0.1", "dev": true, "optional": true}, "pinkie": {"version": "2.0.4", "dev": true, "optional": true}, "pinkie-promise": {"version": "2.0.1", "dev": true, "optional": true, "requires": {"pinkie": "^2.0.0"}}, "pkg-dir": {"version": "4.2.0", "dev": true, "requires": {"find-up": "^4.0.0"}, "dependencies": {"find-up": {"version": "4.1.0", "dev": true, "requires": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}}, "locate-path": {"version": "5.0.0", "dev": true, "requires": {"p-locate": "^4.1.0"}}, "p-limit": {"version": "2.3.0", "dev": true, "requires": {"p-try": "^2.0.0"}}, "p-locate": {"version": "4.1.0", "dev": true, "requires": {"p-limit": "^2.2.0"}}}}, "pngquant-bin": {"version": "6.0.1", "dev": true, "optional": true, "requires": {"bin-build": "^3.0.0", "bin-wrapper": "^4.0.1", "execa": "^4.0.0"}, "dependencies": {"execa": {"version": "4.1.0", "dev": true, "optional": true, "requires": {"cross-spawn": "^7.0.0", "get-stream": "^5.0.0", "human-signals": "^1.1.1", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "onetime": "^5.1.0", "signal-exit": "^3.0.2", "strip-final-newline": "^2.0.0"}}, "get-stream": {"version": "5.2.0", "dev": true, "optional": true, "requires": {"pump": "^3.0.0"}}, "human-signals": {"version": "1.1.1", "dev": true, "optional": true}}}, "portfinder": {"version": "1.0.28", "dev": true, "requires": {"async": "^2.6.2", "debug": "^3.1.1", "mkdirp": "^0.5.5"}, "dependencies": {"debug": {"version": "3.2.7", "dev": true, "requires": {"ms": "^2.1.1"}}}}, "postcss": {"version": "8.3.6", "dev": true, "requires": {"colorette": "^1.2.2", "nanoid": "^3.1.23", "source-map-js": "^0.6.2"}}, "postcss-modules-extract-imports": {"version": "3.0.0", "dev": true, "requires": {}}, "postcss-modules-local-by-default": {"version": "4.0.0", "dev": true, "requires": {"icss-utils": "^5.0.0", "postcss-selector-parser": "^6.0.2", "postcss-value-parser": "^4.1.0"}}, "postcss-modules-scope": {"version": "3.0.0", "dev": true, "requires": {"postcss-selector-parser": "^6.0.4"}}, "postcss-modules-values": {"version": "4.0.0", "dev": true, "requires": {"icss-utils": "^5.0.0"}}, "postcss-selector-parser": {"version": "6.0.6", "dev": true, "requires": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}}, "postcss-value-parser": {"version": "4.1.0", "dev": true}, "prepend-http": {"version": "1.0.4", "dev": true, "optional": true}, "pretty-error": {"version": "3.0.4", "dev": true, "requires": {"lodash": "^4.17.20", "renderkid": "^2.0.6"}}, "process": {"version": "0.11.10", "resolved": "https://registry.npmjs.org/process/-/process-0.11.10.tgz", "integrity": "sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A=="}, "process-nextick-args": {"version": "2.0.1", "dev": true}, "proto-list": {"version": "1.2.4", "dev": true, "optional": true}, "proxy-addr": {"version": "2.0.7", "dev": true, "requires": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}}, "proxy-from-env": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz", "integrity": "sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg=="}, "pseudomap": {"version": "1.0.2", "dev": true, "optional": true}, "pump": {"version": "3.0.0", "dev": true, "optional": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "punycode": {"version": "2.1.1", "dev": true}, "qs": {"version": "6.7.0", "dev": true}, "query-string": {"version": "5.1.1", "dev": true, "optional": true, "requires": {"decode-uri-component": "^0.2.0", "object-assign": "^4.1.0", "strict-uri-encode": "^1.0.0"}}, "queue-microtask": {"version": "1.2.3", "dev": true}, "quick-lru": {"version": "5.1.1", "dev": true}, "randombytes": {"version": "2.1.0", "dev": true, "requires": {"safe-buffer": "^5.1.0"}}, "range-parser": {"version": "1.2.1", "dev": true}, "raw-body": {"version": "2.4.0", "dev": true, "requires": {"bytes": "3.1.0", "http-errors": "1.7.2", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "dependencies": {"bytes": {"version": "3.1.0", "dev": true}}}, "read-pkg": {"version": "6.0.0", "dev": true, "requires": {"@types/normalize-package-data": "^2.4.0", "normalize-package-data": "^3.0.2", "parse-json": "^5.2.0", "type-fest": "^1.0.1"}}, "read-pkg-up": {"version": "8.0.0", "dev": true, "requires": {"find-up": "^5.0.0", "read-pkg": "^6.0.0", "type-fest": "^1.0.1"}}, "readable-stream": {"version": "3.6.0", "dev": true, "requires": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}}, "readable-web-to-node-stream": {"version": "3.0.2", "dev": true, "requires": {"readable-stream": "^3.6.0"}}, "readdirp": {"version": "3.6.0", "dev": true, "requires": {"picomatch": "^2.2.1"}}, "rechoir": {"version": "0.7.1", "dev": true, "requires": {"resolve": "^1.9.0"}}, "redent": {"version": "4.0.0", "dev": true, "requires": {"indent-string": "^5.0.0", "strip-indent": "^4.0.0"}}, "regexp.prototype.flags": {"version": "1.3.1", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.3"}}, "relateurl": {"version": "0.2.7", "dev": true}, "renderkid": {"version": "2.0.7", "dev": true, "requires": {"css-select": "^4.1.3", "dom-converter": "^0.2.0", "htmlparser2": "^6.1.0", "lodash": "^4.17.21", "strip-ansi": "^3.0.1"}}, "replace-ext": {"version": "1.0.1", "dev": true}, "require-from-string": {"version": "2.0.2", "dev": true}, "requires-port": {"version": "1.0.0", "dev": true}, "resolve": {"version": "1.20.0", "dev": true, "requires": {"is-core-module": "^2.2.0", "path-parse": "^1.0.6"}}, "resolve-cwd": {"version": "3.0.0", "dev": true, "requires": {"resolve-from": "^5.0.0"}}, "resolve-from": {"version": "5.0.0", "dev": true}, "responselike": {"version": "1.0.2", "dev": true, "optional": true, "requires": {"lowercase-keys": "^1.0.0"}}, "retry": {"version": "0.13.1", "dev": true}, "reusify": {"version": "1.0.4", "dev": true}, "rimraf": {"version": "3.0.2", "dev": true, "requires": {"glob": "^7.1.3"}}, "rtcpeerconnection-shim": {"version": "1.2.15", "resolved": "https://registry.npmjs.org/rtcpeerconnection-shim/-/rtcpeerconnection-shim-1.2.15.tgz", "integrity": "sha512-C6DxhXt7bssQ1nHb154lqeL0SXz5Dx4RczXZu2Aa/L1NJFnEVDxFwCBo3fqtuljhHIGceg5JKBV4XJ0gW5JKyw==", "requires": {"sdp": "^2.6.0"}}, "run-parallel": {"version": "1.2.0", "dev": true, "requires": {"queue-microtask": "^1.2.2"}}, "safe-buffer": {"version": "5.2.1", "dev": true}, "safer-buffer": {"version": "2.1.2", "dev": true}, "schema-utils": {"version": "3.1.1", "dev": true, "requires": {"@types/json-schema": "^7.0.8", "ajv": "^6.12.5", "ajv-keywords": "^3.5.2"}}, "sdp": {"version": "2.12.0", "resolved": "https://registry.npmjs.org/sdp/-/sdp-2.12.0.tgz", "integrity": "sha512-jhXqQAQVM+8Xj5EjJGVweuEzgtGWb3tmEEpl3CLP3cStInSbVHSg0QWOGQzNq8pSID4JkpeV2mPqlMDLrm0/Vw=="}, "seek-bzip": {"version": "1.0.6", "dev": true, "optional": true, "requires": {"commander": "^2.8.1"}, "dependencies": {"commander": {"version": "2.20.3", "dev": true, "optional": true}}}, "select-hose": {"version": "2.0.0", "dev": true}, "selfsigned": {"version": "2.0.0", "dev": true, "requires": {"node-forge": "^1.2.0"}}, "semver": {"version": "7.3.5", "dev": true, "requires": {"lru-cache": "^6.0.0"}}, "semver-regex": {"version": "2.0.0", "dev": true, "optional": true}, "semver-truncate": {"version": "1.1.2", "dev": true, "optional": true, "requires": {"semver": "^5.3.0"}, "dependencies": {"semver": {"version": "5.7.1", "dev": true, "optional": true}}}, "send": {"version": "0.17.1", "dev": true, "requires": {"debug": "2.6.9", "depd": "~1.1.2", "destroy": "~1.0.4", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "~1.7.2", "mime": "1.6.0", "ms": "2.1.1", "on-finished": "~2.3.0", "range-parser": "~1.2.1", "statuses": "~1.5.0"}, "dependencies": {"debug": {"version": "2.6.9", "dev": true, "requires": {"ms": "2.0.0"}, "dependencies": {"ms": {"version": "2.0.0", "dev": true}}}, "ms": {"version": "2.1.1", "dev": true}}}, "serialize-javascript": {"version": "6.0.0", "dev": true, "requires": {"randombytes": "^2.1.0"}}, "serve-index": {"version": "1.9.1", "dev": true, "requires": {"accepts": "~1.3.4", "batch": "0.6.1", "debug": "2.6.9", "escape-html": "~1.0.3", "http-errors": "~1.6.2", "mime-types": "~2.1.17", "parseurl": "~1.3.2"}, "dependencies": {"debug": {"version": "2.6.9", "dev": true, "requires": {"ms": "2.0.0"}}, "http-errors": {"version": "1.6.3", "dev": true, "requires": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.0", "statuses": ">= 1.4.0 < 2"}}, "inherits": {"version": "2.0.3", "dev": true}, "ms": {"version": "2.0.0", "dev": true}, "setprototypeof": {"version": "1.1.0", "dev": true}}}, "serve-static": {"version": "1.14.1", "dev": true, "requires": {"encodeurl": "~1.0.2", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.17.1"}}, "setprototypeof": {"version": "1.1.1", "dev": true}, "shallow-clone": {"version": "3.0.1", "dev": true, "requires": {"kind-of": "^6.0.2"}}, "shebang-command": {"version": "2.0.0", "requires": {"shebang-regex": "^3.0.0"}}, "shebang-regex": {"version": "3.0.0"}, "signal-exit": {"version": "3.0.3", "dev": true}, "slash": {"version": "3.0.0", "dev": true}, "sockjs": {"version": "0.3.21", "dev": true, "requires": {"faye-websocket": "^0.11.3", "uuid": "^3.4.0", "websocket-driver": "^0.7.4"}}, "sort-keys": {"version": "1.1.2", "dev": true, "optional": true, "requires": {"is-plain-obj": "^1.0.0"}}, "sort-keys-length": {"version": "1.0.1", "dev": true, "optional": true, "requires": {"sort-keys": "^1.0.0"}}, "source-map": {"version": "0.6.1", "dev": true}, "source-map-js": {"version": "0.6.2", "dev": true}, "source-map-support": {"version": "0.5.21", "dev": true, "requires": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "spdx-correct": {"version": "3.1.1", "dev": true, "requires": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}}, "spdx-exceptions": {"version": "2.3.0", "dev": true}, "spdx-expression-parse": {"version": "3.0.1", "dev": true, "requires": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "spdx-license-ids": {"version": "3.0.10", "dev": true}, "spdy": {"version": "4.0.2", "dev": true, "requires": {"debug": "^4.1.0", "handle-thing": "^2.0.0", "http-deceiver": "^1.2.7", "select-hose": "^2.0.0", "spdy-transport": "^3.0.0"}}, "spdy-transport": {"version": "3.0.0", "dev": true, "requires": {"debug": "^4.1.0", "detect-node": "^2.0.4", "hpack.js": "^2.1.6", "obuf": "^1.1.2", "readable-stream": "^3.0.6", "wbuf": "^1.7.3"}}, "stable": {"version": "0.1.8", "dev": true, "optional": true}, "statuses": {"version": "1.5.0", "dev": true}, "strict-uri-encode": {"version": "1.1.0", "dev": true, "optional": true}, "string_decoder": {"version": "1.3.0", "dev": true, "requires": {"safe-buffer": "~5.2.0"}}, "strip-ansi": {"version": "3.0.1", "dev": true, "requires": {"ansi-regex": "^2.0.0"}}, "strip-dirs": {"version": "2.1.0", "dev": true, "optional": true, "requires": {"is-natural-number": "^4.0.1"}}, "strip-eof": {"version": "1.0.0", "dev": true, "optional": true}, "strip-final-newline": {"version": "2.0.0", "dev": true}, "strip-indent": {"version": "4.0.0", "dev": true, "requires": {"min-indent": "^1.0.1"}}, "strip-outer": {"version": "1.0.1", "dev": true, "optional": true, "requires": {"escape-string-regexp": "^1.0.2"}}, "strnum": {"version": "1.0.5", "dev": true, "optional": true}, "strtok3": {"version": "6.2.4", "dev": true, "requires": {"@tokenizer/token": "^0.3.0", "peek-readable": "^4.0.1"}}, "style-loader": {"version": "3.2.1", "dev": true, "requires": {}}, "supports-color": {"version": "5.5.0", "dev": true, "requires": {"has-flag": "^3.0.0"}}, "svgo": {"version": "2.8.0", "dev": true, "optional": true, "requires": {"@trysound/sax": "0.2.0", "commander": "^7.2.0", "css-select": "^4.1.3", "css-tree": "^1.1.3", "csso": "^4.2.0", "picocolors": "^1.0.0", "stable": "^0.1.8"}, "dependencies": {"commander": {"version": "7.2.0", "dev": true, "optional": true}}}, "tapable": {"version": "2.2.0", "dev": true}, "tar-stream": {"version": "1.6.2", "dev": true, "optional": true, "requires": {"bl": "^1.0.0", "buffer-alloc": "^1.2.0", "end-of-stream": "^1.0.0", "fs-constants": "^1.0.0", "readable-stream": "^2.3.0", "to-buffer": "^1.1.1", "xtend": "^4.0.0"}, "dependencies": {"readable-stream": {"version": "2.3.7", "dev": true, "optional": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "safe-buffer": {"version": "5.1.2", "dev": true, "optional": true}, "string_decoder": {"version": "1.1.1", "dev": true, "optional": true, "requires": {"safe-buffer": "~5.1.0"}}}}, "temp-dir": {"version": "2.0.0", "dev": true}, "tempfile": {"version": "2.0.0", "dev": true, "optional": true, "requires": {"temp-dir": "^1.0.0", "uuid": "^3.0.1"}, "dependencies": {"temp-dir": {"version": "1.0.0", "dev": true, "optional": true}}}, "tempy": {"version": "1.0.1", "dev": true, "requires": {"del": "^6.0.0", "is-stream": "^2.0.0", "temp-dir": "^2.0.0", "type-fest": "^0.16.0", "unique-string": "^2.0.0"}, "dependencies": {"type-fest": {"version": "0.16.0", "dev": true}}}, "terser": {"version": "4.8.0", "dev": true, "requires": {"commander": "^2.20.0", "source-map": "~0.6.1", "source-map-support": "~0.5.12"}, "dependencies": {"commander": {"version": "2.20.3", "dev": true}}}, "terser-webpack-plugin": {"version": "5.2.2", "dev": true, "requires": {"jest-worker": "^27.0.6", "p-limit": "^3.1.0", "schema-utils": "^3.1.1", "serialize-javascript": "^6.0.0", "source-map": "^0.6.1", "terser": "^5.7.2"}, "dependencies": {"commander": {"version": "2.20.3", "dev": true}, "terser": {"version": "5.7.2", "dev": true, "requires": {"commander": "^2.20.0", "source-map": "~0.7.2", "source-map-support": "~0.5.19"}, "dependencies": {"source-map": {"version": "0.7.3", "dev": true}}}}}, "through": {"version": "2.3.8", "dev": true, "optional": true}, "thunky": {"version": "1.1.0", "dev": true}, "timed-out": {"version": "4.0.1", "dev": true, "optional": true}, "to-buffer": {"version": "1.1.1", "dev": true, "optional": true}, "to-regex-range": {"version": "5.0.1", "dev": true, "requires": {"is-number": "^7.0.0"}}, "toidentifier": {"version": "1.0.0", "dev": true}, "token-types": {"version": "4.1.1", "dev": true, "requires": {"@tokenizer/token": "^0.3.0", "ieee754": "^1.2.1"}}, "trim-newlines": {"version": "4.0.2", "dev": true}, "trim-repeated": {"version": "1.0.0", "dev": true, "optional": true, "requires": {"escape-string-regexp": "^1.0.2"}}, "ts-loader": {"version": "9.2.5", "dev": true, "requires": {"chalk": "^4.1.0", "enhanced-resolve": "^5.0.0", "micromatch": "^4.0.0", "semver": "^7.3.4"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.2", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "color-convert": {"version": "2.0.1", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "dev": true}, "has-flag": {"version": "4.0.0", "dev": true}, "supports-color": {"version": "7.2.0", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "tslib": {"version": "2.3.1", "dev": true}, "tunnel-agent": {"version": "0.6.0", "dev": true, "optional": true, "requires": {"safe-buffer": "^5.0.1"}}, "type-fest": {"version": "1.4.0", "dev": true}, "type-is": {"version": "1.6.18", "dev": true, "requires": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}}, "typescript": {"version": "4.4.2", "dev": true}, "ua-parser-js": {"version": "0.7.38", "resolved": "https://registry.npmjs.org/ua-parser-js/-/ua-parser-js-0.7.38.tgz", "integrity": "sha512-fYmIy7fKTSFAhG3fuPlubeGaMoAd6r0rSnfEsO5nEY55i26KSLt9EH7PLQiiqPUhNqYIJvSkTy1oArIcXAbPbA=="}, "unbzip2-stream": {"version": "1.4.3", "dev": true, "optional": true, "requires": {"buffer": "^5.2.1", "through": "^2.3.8"}}, "unique-string": {"version": "2.0.0", "dev": true, "requires": {"crypto-random-string": "^2.0.0"}}, "unpipe": {"version": "1.0.0", "dev": true}, "uri-js": {"version": "4.4.1", "dev": true, "requires": {"punycode": "^2.1.0"}}, "url-parse-lax": {"version": "1.0.0", "dev": true, "optional": true, "requires": {"prepend-http": "^1.0.1"}}, "url-to-options": {"version": "1.0.1", "dev": true, "optional": true}, "util-deprecate": {"version": "1.0.2", "dev": true}, "utila": {"version": "0.4.0", "dev": true}, "utils-merge": {"version": "1.0.1", "dev": true}, "uuid": {"version": "3.4.0", "dev": true}, "validate-npm-package-license": {"version": "3.0.4", "dev": true, "requires": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}}, "vary": {"version": "1.1.2", "dev": true}, "watchpack": {"version": "2.2.0", "dev": true, "requires": {"glob-to-regexp": "^0.4.1", "graceful-fs": "^4.1.2"}}, "wbuf": {"version": "1.7.3", "dev": true, "requires": {"minimalistic-assert": "^1.0.0"}}, "web-streams-polyfill": {"version": "3.3.3", "resolved": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.3.3.tgz", "integrity": "sha512-d2JWLCivmZYTSIoge9MsgFCZrt571BikcWGYkjC1khllbTeDlGqZ2D8vD8E/lJa8WGWbb7Plm8/XJYV7IJHZZw=="}, "webpack": {"version": "5.51.2", "dev": true, "requires": {"@types/eslint-scope": "^3.7.0", "@types/estree": "^0.0.50", "@webassemblyjs/ast": "1.11.1", "@webassemblyjs/wasm-edit": "1.11.1", "@webassemblyjs/wasm-parser": "1.11.1", "acorn": "^8.4.1", "acorn-import-assertions": "^1.7.6", "browserslist": "^4.14.5", "chrome-trace-event": "^1.0.2", "enhanced-resolve": "^5.8.0", "es-module-lexer": "^0.7.1", "eslint-scope": "5.1.1", "events": "^3.2.0", "glob-to-regexp": "^0.4.1", "graceful-fs": "^4.2.4", "json-parse-better-errors": "^1.0.2", "loader-runner": "^4.2.0", "mime-types": "^2.1.27", "neo-async": "^2.6.2", "schema-utils": "^3.1.0", "tapable": "^2.1.1", "terser-webpack-plugin": "^5.1.3", "watchpack": "^2.2.0", "webpack-sources": "^3.2.0"}}, "webpack-cli": {"version": "4.10.0", "resolved": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.10.0.tgz", "integrity": "sha512-NLhDfH/h4O6UOy+0LSso42xvYypClINuMNBVVzX4vX98TmTaTUxwRbXdhucbFMd2qLaCTcLq/PdYrvi8onw90w==", "dev": true, "requires": {"@discoveryjs/json-ext": "^0.5.0", "@webpack-cli/configtest": "^1.2.0", "@webpack-cli/info": "^1.5.0", "@webpack-cli/serve": "^1.7.0", "colorette": "^2.0.14", "commander": "^7.0.0", "cross-spawn": "^7.0.3", "fastest-levenshtein": "^1.0.12", "import-local": "^3.0.2", "interpret": "^2.2.0", "rechoir": "^0.7.0", "webpack-merge": "^5.7.3"}, "dependencies": {"colorette": {"version": "2.0.20", "resolved": "https://registry.npmjs.org/colorette/-/colorette-2.0.20.tgz", "integrity": "sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w==", "dev": true}, "commander": {"version": "7.2.0", "dev": true}}}, "webpack-dev-middleware": {"version": "5.3.1", "dev": true, "requires": {"colorette": "^2.0.10", "memfs": "^3.4.1", "mime-types": "^2.1.31", "range-parser": "^1.2.1", "schema-utils": "^4.0.0"}, "dependencies": {"ajv": {"version": "8.10.0", "dev": true, "requires": {"fast-deep-equal": "^3.1.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2", "uri-js": "^4.2.2"}}, "ajv-keywords": {"version": "5.1.0", "dev": true, "requires": {"fast-deep-equal": "^3.1.3"}}, "colorette": {"version": "2.0.16", "dev": true}, "json-schema-traverse": {"version": "1.0.0", "dev": true}, "schema-utils": {"version": "4.0.0", "dev": true, "requires": {"@types/json-schema": "^7.0.9", "ajv": "^8.8.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.0.0"}}}}, "webpack-dev-server": {"version": "4.7.4", "dev": true, "requires": {"@types/bonjour": "^3.5.9", "@types/connect-history-api-fallback": "^1.3.5", "@types/express": "^4.17.13", "@types/serve-index": "^1.9.1", "@types/sockjs": "^0.3.33", "@types/ws": "^8.2.2", "ansi-html-community": "^0.0.8", "bonjour": "^3.5.0", "chokidar": "^3.5.3", "colorette": "^2.0.10", "compression": "^1.7.4", "connect-history-api-fallback": "^1.6.0", "default-gateway": "^6.0.3", "del": "^6.0.0", "express": "^4.17.1", "graceful-fs": "^4.2.6", "html-entities": "^2.3.2", "http-proxy-middleware": "^2.0.0", "ipaddr.js": "^2.0.1", "open": "^8.0.9", "p-retry": "^4.5.0", "portfinder": "^1.0.28", "schema-utils": "^4.0.0", "selfsigned": "^2.0.0", "serve-index": "^1.9.1", "sockjs": "^0.3.21", "spdy": "^4.0.2", "strip-ansi": "^7.0.0", "webpack-dev-middleware": "^5.3.1", "ws": "^8.4.2"}, "dependencies": {"ajv": {"version": "8.10.0", "dev": true, "requires": {"fast-deep-equal": "^3.1.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2", "uri-js": "^4.2.2"}}, "ajv-keywords": {"version": "5.1.0", "dev": true, "requires": {"fast-deep-equal": "^3.1.3"}}, "ansi-regex": {"version": "6.0.1", "dev": true}, "colorette": {"version": "2.0.16", "dev": true}, "ipaddr.js": {"version": "2.0.1", "dev": true}, "json-schema-traverse": {"version": "1.0.0", "dev": true}, "schema-utils": {"version": "4.0.0", "dev": true, "requires": {"@types/json-schema": "^7.0.9", "ajv": "^8.8.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.0.0"}}, "strip-ansi": {"version": "7.0.1", "dev": true, "requires": {"ansi-regex": "^6.0.1"}}}}, "webpack-merge": {"version": "5.8.0", "dev": true, "requires": {"clone-deep": "^4.0.1", "wildcard": "^2.0.0"}}, "webpack-sources": {"version": "3.2.0", "dev": true}, "webrtc-adapter": {"version": "6.4.8", "resolved": "https://registry.npmjs.org/webrtc-adapter/-/webrtc-adapter-6.4.8.tgz", "integrity": "sha512-YM8yl545c/JhYcjGHgaCoA7jRK/KZuMwEDFeP2AcP0Auv5awEd+gZE0hXy9z7Ed3p9HvAXp8jdbe+4ESb1zxAw==", "requires": {"rtcpeerconnection-shim": "^1.2.14", "sdp": "^2.9.0"}}, "websocket-driver": {"version": "0.7.4", "dev": true, "requires": {"http-parser-js": ">=0.5.1", "safe-buffer": ">=5.1.0", "websocket-extensions": ">=0.1.1"}}, "websocket-extensions": {"version": "0.1.4", "dev": true}, "which": {"version": "2.0.2", "requires": {"isexe": "^2.0.0"}}, "wildcard": {"version": "2.0.0", "dev": true}, "wrappy": {"version": "1.0.2", "dev": true}, "ws": {"version": "8.5.0", "dev": true, "requires": {}}, "xtend": {"version": "4.0.2", "dev": true, "optional": true}, "yallist": {"version": "4.0.0", "dev": true}, "yargs-parser": {"version": "20.2.9", "dev": true}, "yauzl": {"version": "2.10.0", "dev": true, "optional": true, "requires": {"buffer-crc32": "~0.2.3", "fd-slicer": "~1.1.0"}}, "yocto-queue": {"version": "0.1.0", "dev": true}}}