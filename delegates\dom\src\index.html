<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="description" content="" />


    <!-- <script type="text/javascript" src="https://oc-gpt-widget.onrender.com/chat-widget.umd.cjs"></script>
<link rel="stylesheet" href="https://oc-gpt-widget.onrender.com/style.css"> -->


  <!-- <script type="text/javascript" src="https://oc-gpt-widget.onrender.com/chat-widget.umd.cjs"></script> -->
  <!-- <script type="text/javascript" src="http://127.0.0.1:5500/chat-widget.umd.cjs"></script> -->
  <!-- <link rel="stylesheet" href="https://oc-gpt-widget.onrender.com/style.css"> -->
  <!-- <link rel="stylesheet" href="http://127.0.0.1:5500/style.css"> -->
<!--
  <script type="text/javascript" src="https://oc-gpt-widget-old-sps.netlify.app/chat-widget.umd.cjs"></script>
  <link rel="stylesheet" href="https://oc-gpt-widget-old-sps.netlify.app/style.css"> -->

   <script type="text/javascript" src="https://assistant-widget.netlify.app/assistant-widget.umd.cjs"></script>
    <link rel="stylesheet" href="https://assistant-widget.netlify.app/style.css">

    <link
      id="favSvg"
      rel="icon"
      type="image/svg+xml"
      href="./assets/images/favicon.svg"
    />
    <link
      id="favPng"
      rel="icon"
      type="image/png"
      href="./assets/images/favicon.png"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css"
    />

    <!-- <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css"
      integrity="sha512-t4GWSVZO1eC8BM339Xd7Uphw5s17a86tIZIj8qRxhnKub6WoyhnrxeCIMeAqBPgdZGlCcG2PrZjMc+Wr78+5Xg=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap-grid.min.css"
      integrity="sha512-EAgFb1TGFSRh1CCsDotrqJMqB2D+FLCOXAJTE16Ajphi73gQmfJS/LNl6AsjDqDht6Ls7Qr1KWsrJxyttEkxIA=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap-reboot.min.css"
      integrity="sha512-allly0sW31f5fb2FdiSuezu/pLSoV8hN0liPTS5PAeB39hxh5S6yDf6ak7ge77JkfnOkiFNfjdXDyx6sEzz08A=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap-utilities.min.css"
      integrity="sha512-K4XWKeYNHW67orY92NwVkgHAShgq/TowE5Sx9O4imSO1YM3ll+6pLLwcSJvr3IwDIWCnSDhkuxxqJEbY8+iGzQ=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    /> -->
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.min.js" integrity="sha512-3dZ9wIrMMij8rOH7X3kLfXAzwtcHpuYpEgQg1OA4QAob1e81H8ntUQmQm3pBudqIoySO5j0tHN4ENzA6+n2r4w==" crossorigin="anonymous" referrerpolicy="no-referrer"></script> -->

    <!-- <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script> -->
    <!-- <script src="./dist/nipplejs.min.js"></script>
    <script>
        var dynamic = nipplejs.create({
            zone: document.getElementById('dynamic'),
            color: 'blue'
        });
    </script> -->
    <!-- <div id="joystick-left" style="position: fixed; width: 50%; top:0; bottom: 0">

    </div>
    <div id="joystick-right" style="position: fixed; width: 50%; left:50%;top:0; bottom: 0;">


    </div> -->
    <!-- Google tag (gtag.js) -->
    <script
      async
      src="https://www.googletagmanager.com/gtag/js?id=G-11EWDENKJX"
    ></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag("js", new Date());

      gtag("config", "G-11EWDENKJX");
    </script>
    <script type="text/javascript">


      var isFirefox = navigator.userAgent.toLowerCase().indexOf("firefox") > -1;
      if (isFirefox) {
        window.navigator.mozGetUserMedia = function () {};
        if (window.navigator.mediaDevices === undefined) {
          navigator.mediaDevices = {};
          navigator.mediaDevices.getSupportedConstraints = function () {
            return {};
          };
          navigator.mediaDevices.getUserMedia = function () {
            return {};
          };
        }
      }

      // window.addEventListener(
      //   "message",
      //   function (event) {
      //     // Check the origin of the message to ensure it's from a trusted source
      //     // if (event.origin !== "http://your-website.com")
      //     //     return;

      //     // The user's information is contained in event.data
      //     var userInfo = event.data;
      //     var username = userInfo.username;
      //     var email = userInfo.email;
      //     var token = userInfo.token;
      //     // console.log("userInfo from iframe", userInfo);

      //     // Do something with the username, email, and token
      //   },
      //   false
      // );
    </script>



    <title>The Omniverse City</title>
  </head>

  <body>
   <div id="infoWindow" hidden>
    <div id="iw-title">title</div>
    <div id="iw-image">image</div>
    <div id="iw-info">info</div>
    <div id="iw-links">

    </div>
   </div>
  <div hidden style="display:flex">
    <button type="button" id="agora-create">Create</button>
    <button type="button" id="agora-join">Join</button>
    <button type="button" id="agora-leave">Leave</button>
  </div>



    <nav class="navbar navbar-expand-lg navbar-dark text-white" id="control-bar">
      <!-- <button id="testbtn">test button</button> -->


     <button id="join-conf" hidden>join</button>
      <div class="container-fluid justify-content-between align-content-center">
        <h1 class="fs-6">The Omniverse City</h1>
        <div id="omniverse-controls" style="
    display: flex;
    vertical-align: middle;
    align-items: center;
">
          <span id="ocx"></span>
          <span class="ps-2"> </span>
          <div style="width: 40px;height: 40px;overflow: hidden;border-radius: 50%;margin-right: 0.5rem;">
            <img src="https://models.readyplayer.me/64e3055495439dfcf3f0b665.png?size=256" alt="" id="avatar" style="
                width: 80px; /* Increase size to zoom */
                height: auto;
                object-fit: cover;
                object-position: -20px;

            ">
        </div>
          <span id="username">guest</span>
          <span class="ps-2"> </span>
          <button id="peoplePanelBtn" class="btn text-white" type="button" data-bs-toggle="offcanvas" data-bs-target="#peoplePanel" aria-controls="peoplePanel"><i class="fas fa-users"></i></button>
          <button class="btn text-white" type="button" data-bs-toggle="offcanvas" data-bs-target="#settingsPanel" aria-controls="settingsPanel"><i class="fas fa-cog"></i></button>
          <!-- <button class="btn text-white" type="button" data-bs-toggle="offcanvas" data-bs-target="#chatPanel" aria-controls="chatPanel"><i class="far fa-comment-alt"></i></button> -->

          <button id="host-conf" type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#hostConfModal">Meet People</button>
          <!-- <button id="testbtn" type="button" class="btn btn-primary btn-sm">Load</button> -->

        </span>
       </div>



      </div>
    </nav>




<div class="offcanvas offcanvas-end" tabindex="-1" id="settingsPanel" aria-labelledby="settingsPanelLabel">
  <div class="offcanvas-header">
    <h5 class="offcanvas-title" id="settingsPanelLabel">Settings</h5>
    <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
  </div>
  <div class="offcanvas-body">
    <div class="list-group">
      <div class="list-group-item">
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" role="switch" id="muteMicToggle" checked>
          <label class="form-check-label" for="muteMicToggle">Mic input</label>
        </div>
      </div>

      <div class="list-group-item">
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" role="switch" id="toggleMiniMap" checked>
          <label class="form-check-label" for="toggleMiniMap">Minimap</label>
        </div>
      </div>

      <div class="list-group-item">
        <div>
          <label for="musicVolume" class="form-label">Music Volume</label>
          <input type="range" class="form-range" id="musicVolume" value="50">
        </div>
      </div>

      <div class="list-group-item">
        <div>
          <label for="confVolume" class="form-label">Conference Volume</label>
          <input type="range" class="form-range" id="confVolume" value="50">
        </div>
      </div>

    </div>
  </div>
</div>

<div class="offcanvas offcanvas-end" tabindex="-1" id="poiPanel" aria-labelledby="poiPanelLabel">
  <div class="offcanvas-header">
    <h5 class="offcanvas-title" id="poiPanelLabel">Teleport to</h5>
    <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
  </div>
  <div class="offcanvas-body">
    <div id="poi">


      <ul id="poi-list">
        <!-- Participants will be added here dynamically -->
        <!-- <li>
          <a class="poiButton" href="#">aa</a>
        </li>
        <li>
          <a class="poiButton" href="#">aa</a>
        </li>
        <li>
          <a class="poiButton" href="#">aa</a>
        </li>
        <li>
          <a class="poiButton" href="#">aa</a>
        </li> -->
      </ul>
    </div>
  </div>
</div>
<div class="offcanvas offcanvas-end" tabindex="-1" id="peoplePanel" aria-labelledby="peoplePanelLabel">
  <div class="offcanvas-header">
    <h5 class="offcanvas-title" id="peoplePanelLabel">People</h5>
    <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
  </div>
  <div class="offcanvas-body">
    <div id="people">
      <div class="w-100">
        <!-- <button  id="solo-btn" type="button" class="btn btn-primary btn-lg w-100" data-bs-toggle="modal" data-bs-target="#hostConfModal">Meet People</button> -->
        <!-- <button  id="join-conf" type="button" class="btn">Join</button> -->
        <!-- Button trigger modal -->
      </div>

      <ul id="participants-list">
        <!-- Participants will be added here dynamically -->
        <!-- <li>
          <img src="https://api.readyplayer.me/v1/avatars/636d90b73c0e9a4191dc9660.png" alt="Profile Picture">
          <div class="name">test</div>
          <div class="mute-btn">🔇</div>
          </li><li>
          <img src="https://api.readyplayer.me/v1/avatars/636d90b73c0e9a4191dc9660.png" alt="Profile Picture">
          <div class="name">test</div>
          <div class="mute-btn">🔇</div>
          </li> -->
      </ul>
    </div>
  </div>
</div>

<div class="offcanvas offcanvas-end" tabindex="-1" id="chatPanel" aria-labelledby="settingsPanelLabel">
  <div class="offcanvas-header">
    <h5 class="offcanvas-title" id="settingsPanelLabel">Chat</h5>
    <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
  </div>
  <div class="offcanvas-body">
    ...
  </div>
</div>




<!-- Modal -->
<div class="modal fade" id="hostConfModal" tabindex="-1" aria-labelledby="hostConfModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h1 class="modal-title fs-5" id="hostConfModalLabel">Meet People</h1>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="input-group mb-3">
          <input id="session-id-input" type="text" class="form-control bg-white" placeholder="Session ID" aria-label="Session ID" aria-describedby="join-session-btn" >
          <button class="btn btn-primary" type="button" id="join-session-btn" data-bs-dismiss="modal">Join</button>
        </div>
      </div>
      <div class="modal-footer">



        <button id="rejoin-session-btn" type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Rejoin last meeting</button>
        <button id="create-session-btn" type="button" class="btn btn-primary" data-bs-dismiss="modal">Create meeting</button>
      </div>
    </div>
  </div>
</div>

    <!-- Modal -->

    <div
      class="modal fade modal-xl"
      id="exampleModal"
      tabindex="-1"
      aria-labelledby="exampleModalLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog w-100 h-100">
        <div class="modal-content" style="height: 80%">
          <div class="modal-header">
            <!-- <h5 class="modal-title" id="exampleModalLabel">Modal title</h5> -->
            <button
              type="button"
              class="btn-close btn-sm"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body" id="iframe-wrapper">
            <!-- <iframe id="iFrameOverlay" src="https://www.theomniverse.city" frameborder="0" width="100%" height="100%"></iframe> -->
          </div>
          <!-- <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
              <button type="button" class="btn btn-primary">Save changes</button>
            </div> -->
        </div>
      </div>
    </div>

    <!-- <iframe
      id="frame"
      class="frame"
      allow="camera *; microphone *; clipboard-write"
      allowtransparency
      hidden
    ></iframe> -->

    <!-- <video id="video-background" loop hidden>
        <source src="https://theomniverse.city/wp-content/uploads/2023/07/cfc32a5c-8c80-4dd3-8667-79bf88aee000.mp4" type="video/mp4">
    </video> -->
    <div id="overlay">

    </div>

    <div
    
    id="am-container"
    style="
        width: 100vw;
        height: 100vh;
        margin: auto;
        border: 1px solid black;
    "
    >






<!--

    <div id="arcane-player"
    tabindex="1"
    data-project-id="3353"
    data-project-key="cfcb76c9-43a1-4144-9cef-5ed0a9c732be"
    data-idle-timeout="300"
    data-enable-events-passthrough="true"
    data-autoplay="false"
    data-hide-ui-controls="true"
    data-token="ABmqXUCu-dXi"
    data-intro-thumbnail="https://images.arcanemirage.com/projects/thumbnails/7ef3fd3b-f729-4689-9f6b-a26b74333f85_1120x630.jpeg"
    ></div>
  <script
  src="https://embed.arcanemirage.com/ba1e38bd-7254-4e3b-973d-b9bf18130b90/e"
  defer onload="init()">
  </script> -->








  <!-- StreamPixel player container -->
  <div id="streampixel-container" style="position: absolute; top: 0; left: 0; width: 100vw; height: 100vh; z-index: 5; background-color: #000;"></div>



    </div>


    <div id="chat" hidden></div>


    <script src="https://download.agora.io/sdk/release/AgoraRTC_N.js"></script>

  </body>
</html>



