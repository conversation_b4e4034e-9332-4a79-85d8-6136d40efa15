# DOM Delegate

This is the DOM delegate implementation for the Optical Omniverse frontend.

## Environment Variables

The application uses environment variables to configure the Arcane Mirage embed code. This allows you to switch between different environments without modifying the code.

### Configuration

Create a `.env` file in the root directory of the project with the following variables:

```
# Arcane Mirage Configuration
ARCANE_PROJECT_ID=5865
ARCANE_PROJECT_KEY=0bfb7bf8-4e6e-4929-a380-ed25244af9da
ARCANE_IDLE_TIMEOUT=300
ARCANE_ENABLE_EVENTS_PASSTHROUGH=true
ARCANE_AUTOPLAY=false
ARCANE_HIDE_UI_CONTROLS=true
ARCANE_TOKEN=vWxjz8OgTYM0
ARCANE_INTRO_THUMBNAIL=https://images.arcanemirage.com/projects/thumbnails/7ef3fd3b-f729-4689-9f6b-a26b74333f85_1120x630.jpeg
ARCANE_EMBED_SCRIPT=https://embed.arcanemirage.com/ba1e38bd-7254-4e3b-973d-b9bf18130b90/e
```

You can modify these values to match your environment.

### Default Values

If environment variables are not provided, the application will use the following default values:

- `ARCANE_PROJECT_ID`: 5865
- `ARCANE_PROJECT_KEY`: 0bfb7bf8-4e6e-4929-a380-ed25244af9da
- `ARCANE_IDLE_TIMEOUT`: 300
- `ARCANE_ENABLE_EVENTS_PASSTHROUGH`: true
- `ARCANE_AUTOPLAY`: false
- `ARCANE_HIDE_UI_CONTROLS`: true
- `ARCANE_TOKEN`: vWxjz8OgTYM0
- `ARCANE_INTRO_THUMBNAIL`: https://images.arcanemirage.com/projects/thumbnails/7ef3fd3b-f729-4689-9f6b-a26b74333f85_1120x630.jpeg
- `ARCANE_EMBED_SCRIPT`: https://embed.arcanemirage.com/ba1e38bd-7254-4e3b-973d-b9bf18130b90/e

## Development

To start the development server:

```bash
npm run serve
```

To build the project:

```bash
npm run build
```

## Switching Environments

To switch between different environments, simply update the `.env` file with the appropriate values for each environment. The application will use these values when built.

For example, to switch to a development environment, you might use:

```
ARCANE_PROJECT_ID=5865
ARCANE_PROJECT_KEY=0bfb7bf8-4e6e-4929-a380-ed25244af9da
ARCANE_TOKEN=devToken123
ARCANE_EMBED_SCRIPT=https://dev-embed.arcanemirage.com/ba1e38bd-7254-4e3b-973d-b9bf18130b90/e
```

And for production:

```
ARCANE_PROJECT_ID=5865
ARCANE_PROJECT_KEY=0bfb7bf8-4e6e-4929-a380-ed25244af9da
ARCANE_TOKEN=prodToken456
ARCANE_EMBED_SCRIPT=https://embed.arcanemirage.com/ba1e38bd-7254-4e3b-973d-b9bf18130b90/e
```

This allows you to easily switch between environments without modifying the code.
