// Global type declarations for StreamPixel SDK

declare module 'streampixelsdk' {
  export interface StreamPixelStream {
    emitUIInteraction: (data: { message: any }) => void;
  }

  export interface StreamPixelApp {
    rootElement: HTMLElement;
    stream: StreamPixelStream;
  }

  export interface PixelStreaming {
    addResponseEventListener: (name: string, listener: (payload: any) => void) => void;
    removeResponseEventListener: (name: string, listener: (payload: any) => void) => void;
    disconnect: () => void;
    unmuteMicrophone: (enabled: boolean) => void;
  }

  export interface UIControl {
    toggleAudio: () => void;
    handleResMax: (resolution: string) => void;
    getResolution: () => string[];
    getStreamStats: () => any;
  }

  export interface StreamPixelApplicationResult {
    appStream: StreamPixelApp;
    pixelStreaming: PixelStreaming;
    UIControl: UIControl;
    queueHandler?: (callback: (msg: any) => void) => void;
  }

  export interface StreamPixelConfig {
    appId: string;
    AutoConnect?: boolean;
    useMic?: boolean;
    touchInput?: boolean;
    mouseInput?: boolean;
    keyBoardInput?: boolean;
    hoverMouse?: boolean;
    gamepadInput?: boolean;
    xrInput?: boolean;
    fakeMouseWithTouches?: boolean;
    afktimeout?: number;
    primaryCodec?: string;
    fallBackCodec?: string;
    startResolution?: string;
    startResolutionMobile?: string;
    startResolutionTab?: string;
    maxStreamQuality?: string;
    resolutionMode?: string;
    minBitrate?: number;
    maxBitrate?: number;
    minQP?: number;
    maxQP?: number;
  }

  export function StreamPixelApplication(config: StreamPixelConfig): Promise<StreamPixelApplicationResult>;
}
