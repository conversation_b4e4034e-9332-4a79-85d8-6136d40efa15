<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <link rel="icon" type="image/svg+xml" href="../../../assets/favicon.ico" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>Spatial Audio Demo -- Agora</title>
  <link rel="stylesheet" href="../../../assets/bootstrap.min.css">
  <link rel="stylesheet" href="../../../common/common.css">
</head>

<body>

  <div class="container">
    <!-- left -->
    <section class="left">
    </section>
    <!-- right -->
    <section class="right row">
      <!-- video -->
      <div class="video-group col-md-12 col-lg-6">
        <!-- local stream -->
        <section class="card">
          <div class="card-header">
            local stream
          </div>
          <div class="card-body">
            <div id="local-player" class="player">
              <div class="card-text local-player-name" id="local-player-name"></div>
            </div>
          </div>
        </section>
        <!-- remote stream -->
        <section class="card">
          <div class="card-header">
            remote stream
          </div>
          <div class="card-body">
            <div id="remote-playerlist"></div>
          </div>
        </section>
      </div>
      <!-- form -->
      <div class="form-wrapper col-lg-6">
        <form id="join-form" name="join-form">
          <div class="join-info-group">
            <div class="form-item">
              <label class="form-label">Channel</label>
              <input class="form-control" id="channel" type="text" placeholder="enter channel name" required>
              <div class="tips">If you don`t know what is your channel, checkout <a
                  href="https://docs.agora.io/en/Agora%20Platform/terms?platform=All%20Platforms#channel">this</a></div>
            </div>
            <div class="form-item">
              <label class="form-label">User ID(optional)</label>
              <input class="form-control" id="uid" type="text" onkeyup="this.value=this.value.replace(/[^0-9]/g,'')"
                onafterpaste="this.value=this.value.replace(/[^0-9]/g,'')" placeholder="Enter the user ID">
            </div>
          </div>
          <div class="button-group">
            <button id="join" type="submit" class="btn btn-primary btn-sm">Join</button>
            <button id="leave" type="button" class="btn btn-primary btn-sm" disabled>Leave</button>
          </div>
          <section>
            <section class="mt-2">
              <div><b>azimuth </b><i style="color: blue;">([0-360]: speaker azimuth in a spherical coordinate system
                  centered on
                  the listener)</i></div>
              <div class="flex">
                <input id="azimuth" class="form-range" type="range" min="0" max="360" value="0" step="1">
                <span id="azimuthValue" class="mx-2">0</span>
              </div>
            </section>
            <section class="mt-2">
              <div><b>elevation </b><i style="color: blue;">([-90-90]: speaker elevation in a spherical coordinate
                  system
                  centered
                  on the listener)</i>
              </div>
              <div class="flex">
                <input class="form-range" id="elevation" type="range" min="-90" max="90" value="0" step="1">
                <span class="mx-2" id="elevationValue">0</span>
              </div>
            </section>
            <section class="mt-2">
              <div><b>distance </b><i style="color: blue;">([1-50]: distance between speaker and listener)</i></div>
              <div class="flex">
                <input class="form-range" id="distance" type="range" min="1" max="50" value="1" step="1">
                <span class="mx-2" id="distanceValue">1</span>
              </div>
            </section>
            <section class="mt-2">
              <div><b>orientation </b><i style="color: blue;">([0-180]: 0 degree is the same with listener
                  orientation)</i>
              </div>
              <div class="flex">
                <input class="form-range" id="orientation" type="range" min="0" max="180" value="180" step="1">
                <span class="mx-2" id="orientationValue">180</span>
              </div>
            </section>
            <section class="mt-2">
              <div><b>attenuation </b><i style="color: blue;">([0-1]: 1 maximum attenuation, 0 no attenuation)</i></div>
              <div class="flex">
                <input class="form-range" id="attenuation" type="range" min="0" max="1" value="0.5" step="0.01">
                <span class="mx-2" id="attenuationValue">0.5</span>
              </div>
            </section>
          </section>

          <section>
            <section class="mt-2">
              <input type="checkbox" id="blurcbx" name="blur" style="width: fit-content;height: fit-content;">
              <label for="blur"><b> Enable Blur</b></label>
              <button class="btn btn-primary btn-sm" type="button" id="blurbtn">Confirm</button>
            </section>
            <section class="mt-2">
              <input type="checkbox" id="airabsorbcbx" name="airabsorb" style="width: fit-content;height: fit-content;"
                checked>
              <label for="airabsorb"><b> Enable Air Absorb</b></label>
              <button class="btn btn-primary btn-sm" type="button" id="airabsorbbtn">Confirm</button>
            </section>
          </section>

        </form>
      </div>
      <!-- app info -->
      <div id="app-info"></div>
    </section>
  </div>


  <script src="../../../assets/jquery-3.4.1.min.js"></script>
  <script src="../../../assets/bootstrap.bundle.min.js"></script>
  <script src="../../../common/constant.js"></script>
  <script src="../../../common/utils.js"></script>
  <script src="../../../common/left-menu.js"></script>
  <script src="../../../i18n/language.js"></script>
  <script src="https://download.agora.io/sdk/release/AgoraRTC_N.js"></script>
  <script src="./index.js" type="module"></script>
</body>

</html>
