body {
	margin: 0px;
	background-color: black;
	background-image: url("https://theomniverse.city/wp-content/uploads/2023/04/FuturisticCity.png");
	font-family: 'Montserrat', sans-serif;
	overflow: hidden; /* Prevent scrollbars during loading */
}

/* Fix for modal backdrop issues */
.modal-backdrop {
  opacity: 0.5;
}

/* Ensure modal is hidden properly when not active */
.modal:not(.show) {
  display: none !important;
  pointer-events: none !important;
}

/* Ensure modal backdrop is hidden properly when not needed */
.modal-backdrop:not(.show) {
  display: none !important;
  pointer-events: none !important;
}

/* Arcane Player Container Styles */
#arcane-player {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 5;
  background-color: #000; /* Black background */
}



#control-bar{
  position: fixed;
  z-index: 6;
  left:0;
  right:0;
z-index: 999;
filter: drop-shadow(0 0 0.75rem black);


background: rgba(0,0,0,0.8);
backdrop-filter: saturate(180%) blur(10px);
}
/* #frame{

    width:90vw;
    height:90vh;
    position: absolute;
    top:7vh;
    bottom:5vh;
    left:5vw;
    z-index: 999;
    background-color: transparent;
    border-radius: 20px;
    min-width: 400px;
} */

  #people ul {
    list-style: none;
    padding: 0;
  }

  #people ul li {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    margin-top: 1rem;
  }

  #people ul li img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    border: 1px solid gray;
  }

  #people ul li .name {
    flex-grow: 1;
  }

  #people ul li .mute-btn {
    font-size: 20px;
    color: #000;
    margin-left: 10px;
    cursor: pointer;
  }

#logo{
  height: 32px;
}

#overlay{
  width: 100vw;
  height: 100vh;
  background-color: rgba(0,0,0,0.2);
  position: absolute;
}


/* General reset for list styles */
ul {
  list-style-type: none; /* Remove bullet points */
  padding: 0;
}

li {
  margin-bottom: 10px; /* Add spacing between items */
}

.poiButton {
  display: inline-block;
  width: 100%; /* Make it full-width */
  padding: 10px 15px; /* Add some padding */
  background-color: #007bff; /* Background color (blue in this case) */
  color: white; /* White text color */
  text-decoration: none; /* Remove underline */
  border: none; /* No border */
  border-radius: 4px; /* Rounded corners */
  font-size: 16px; /* Adjust text size */
  font-weight: bold; /* Make text bold */
  text-align: center; /* Center align text */
  cursor: pointer; /* Pointer cursor on hover */
  transition: background-color 0.3s ease; /* Smooth background color transition */
}

/* Hover state for a more interactive feel */
.poiButton:hover {
  background-color: #0056b3; /* Darker shade of blue on hover */
}

/* Optional: Add a focus/active state for accessibility */
.poiButton:focus, .poiButton:active {
  outline: none;
  background-color: #004085; /* Even darker blue when focused/active */
}
