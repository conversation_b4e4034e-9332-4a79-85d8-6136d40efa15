// This file loads StreamPixel configuration from environment variables
// It exports configuration that will be used by the StreamPixel initialization

export const streamPixelConfig = {
  // StreamPixel App ID from environment or default
  appId: process.env.STREAMPIXEL_APP_ID || 'your-app-id',

  // Connection settings (note: StreamPixel expects AutoConnect with capital A)
  AutoConnect: process.env.STREAMPIXEL_AUTO_CONNECT === 'true' || true,

  // Input settings
  useMic: process.env.STREAMPIXEL_USE_MIC === 'true' || false,
  touchInput: process.env.STREAMPIXEL_TOUCH_INPUT === 'true' || true,
  mouseInput: process.env.STREAMPIXEL_MOUSE_INPUT === 'true' || true,
  keyBoardInput: process.env.STREAMPIXEL_KEYBOARD_INPUT === 'true' || true,
  hoverMouse: process.env.STREAMPIXEL_HOVER_MOUSE === 'true' || true,
  gamepadInput: process.env.STREAMPIXEL_GAMEPAD_INPUT === 'true' || false,
  xrInput: process.env.STREAMPIXEL_XR_INPUT === 'true' || false,
  fakeMouseWithTouches: process.env.STREAMPIXEL_FAKE_MOUSE_TOUCHES === 'true' || true,

  // Session settings
  afktimeout: parseInt(process.env.STREAMPIXEL_AFK_TIMEOUT) || 300,

  // Video settings
  primaryCodec: process.env.STREAMPIXEL_PRIMARY_CODEC || 'H264',
  fallBackCodec: process.env.STREAMPIXEL_FALLBACK_CODEC || 'VP8',
  startResolution: process.env.STREAMPIXEL_START_RESOLUTION || '1080p (1920x1080)',
  startResolutionMobile: process.env.STREAMPIXEL_START_RESOLUTION_MOBILE || '720p (1280x720)',
  startResolutionTab: process.env.STREAMPIXEL_START_RESOLUTION_TAB || '1080p (1920x1080)',
  maxStreamQuality: process.env.STREAMPIXEL_MAX_STREAM_QUALITY || '1080p',
  resolutionMode: process.env.STREAMPIXEL_RESOLUTION_MODE || 'Fixed Resolution Mode',

  // Bitrate settings (optional)
  minBitrate: process.env.STREAMPIXEL_MIN_BITRATE ? parseInt(process.env.STREAMPIXEL_MIN_BITRATE) : undefined,
  maxBitrate: process.env.STREAMPIXEL_MAX_BITRATE ? parseInt(process.env.STREAMPIXEL_MAX_BITRATE) : undefined,
  minQP: process.env.STREAMPIXEL_MIN_QP ? parseInt(process.env.STREAMPIXEL_MIN_QP) : undefined,
  maxQP: process.env.STREAMPIXEL_MAX_QP ? parseInt(process.env.STREAMPIXEL_MAX_QP) : undefined,
};
