{"name": "sps-delegate-dom", "version": "0.1.0", "description": "The reference delegate implementation for consuming the Scalable Pixel Streaming frontend library", "main": "index.ts", "scripts": {"build": "set NODE_OPTIONS=--openssl-legacy-provider && npx webpack", "watch": "cross-env NODE_OPTIONS=--openssl-legacy-provider && npx webpack --watch", "start": "cross-env NODE_OPTIONS=--openssl-legacy-provider && npx webpack && open-cli ./dist/index.html", "serve": "set NODE_OPTIONS=--openssl-legacy-provider && webpack serve", "symlink": "cross-env NODE_OPTIONS=--openssl-legacy-provider && cd ../../library && npm link && cd ../delegates/dom && npm link @tensorworks/libspsfrontend", "build-all": "cross-env NODE_OPTIONS=--openssl-legacy-provider && cd ../../library && npm i && npm run build && cd ../delegates/dom && npm i && npm run symlink && npm run build"}, "author": "TensorWorks Pty Ltd", "license": "MIT", "devDependencies": {"@tensorworks/libspsfrontend": "0.1.4", "@types/jquery": "^3.5.14", "bootstrap": "^5.3.0", "css-loader": "^6.2.0", "dotenv-webpack": "^8.1.0", "html-loader": "^4.1.0", "html-webpack-plugin": "^5.3.2", "image-webpack-loader": "^8.1.0", "mini-css-extract-plugin": "^2.1.0", "open-cli": "^7.0.0", "style-loader": "^3.2.1", "ts-loader": "^9.2.4", "typescript": "^4.3.5", "webpack": "^5.46.0", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.7.4"}, "dependencies": {"@types/bootstrap": "^5.2.10", "@voxeet/voxeet-web-sdk": "^3.11.0-beta.1", "agora-rtc-sdk-ng": "^4.21.0", "axios": "^1.7.2", "cross-env": "^7.0.3", "nipplejs": "^0.10.1"}}