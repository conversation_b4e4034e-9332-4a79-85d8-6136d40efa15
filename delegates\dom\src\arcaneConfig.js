// This file loads Arcane Mirage configuration from environment variables
// It's loaded by the HTML template and sets the configuration dynamically

document.addEventListener('DOMContentLoaded', function() {
  // Get the arcane-player element
  const arcanePlayer = document.getElementById('arcane-player');

  if (arcanePlayer) {
    // Set attributes from environment variables or use defaults
    arcanePlayer.setAttribute('data-project-id', process.env.ARCANE_PROJECT_ID || '5865');
    arcanePlayer.setAttribute('data-project-key', process.env.ARCANE_PROJECT_KEY || '0bfb7bf8-4e6e-4929-a380-ed25244af9da');
    arcanePlayer.setAttribute('data-idle-timeout', process.env.ARCANE_IDLE_TIMEOUT || '300');
    arcanePlayer.setAttribute('data-enable-events-passthrough', process.env.ARCANE_ENABLE_EVENTS_PASSTHROUGH || 'true');
    arcanePlayer.setAttribute('data-autoplay', process.env.ARCANE_AUTOPLAY || 'false');
    arcanePlayer.setAttribute('data-hide-ui-controls', process.env.ARCANE_HIDE_UI_CONTROLS || 'true');
    arcanePlayer.setAttribute('data-token', process.env.ARCANE_TOKEN || 'vWxjz8OgTYM0');
    arcanePlayer.setAttribute('data-intro-thumbnail', process.env.ARCANE_INTRO_THUMBNAIL || 'https://images.arcanemirage.com/projects/thumbnails/7ef3fd3b-f729-4689-9f6b-a26b74333f85_1120x630.jpeg');

    // Create and append the script element
    const scriptSrc = process.env.ARCANE_EMBED_SCRIPT || 'https://embed.arcanemirage.com/ba1e38bd-7254-4e3b-973d-b9bf18130b90/e';
    const script = document.createElement('script');
    script.src = scriptSrc;
    script.defer = true;

    // Log when script is loaded
    script.onload = function() {
      console.log('Arcane Mirage script loaded');

      // Initialize the application
      if (typeof init === 'function') {
        init();
      }
    };

    // Find the parent element of arcanePlayer
    const parent = arcanePlayer.parentNode;

    // Insert the script after arcanePlayer
    if (parent) {
      parent.insertBefore(script, arcanePlayer.nextSibling);
    }
  }
});
