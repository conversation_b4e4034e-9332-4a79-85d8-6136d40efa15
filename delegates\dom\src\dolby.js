import VoxeetSDK from '@voxeet/voxeet-web-sdk';


const to = async (func, params, errorHandlers = [])=>{
    let res, err;
    try{
        res = await func(...(params || []));
    }catch(e){
        err = e;
    }

    if(err && errorHandlers.length){
        return [errorHandlers.reduce((acc, e) => e(acc), err)]
    }

    return [err, res];
}

// const shareVideo = async (participant, stream) => {
//     let perspective = 'self-view';
//     if (VoxeetSDK.session.participant.id !== participant.id) {
//         perspective = 'remote-view';
//     }

//     let videoNode = document.getElementById(`video-${participant.id}`);
//     if (!videoNode) {
//         videoNode = document.createElement("video");

//         videoNode.setAttribute("id", `video-${participant.id}`);
//         videoNode.setAttribute("height", "100%");
//         videoNode.setAttribute("width", "100%");
    
//         videoNode.muted = true;
//         videoNode.autoplay = true;
//         videoNode.playsinline = true;
    
//         const videoContainer = document.getElementById(perspective);
//         videoContainer.lastElementChild.replaceWith(videoNode);
//         videoContainer.firstElementChild.innerText = participant.info.name;
//     }

//     navigator.attachMediaStream(videoNode, stream);
//     console.log(`Step 4: Video of participant '${participant.info.name}' started.`);
// }



// const joinConf = async ()=>{
//     console.log('joining session')
//     await createAndJoinConference('testConf', 'host');
// }
// const leave = async ()=>{
//     console.log('joining session')
//     await leaveConference();
// }



export class Dolby{

    constructor(container){
        // this.container = container;
        // this.panel = document.createElement('div');
        // container.appendChild(this.panel)
        // container.classList.add("dolbyPanel")
    }
    
    leaveConference = async () => {
        try {
            await VoxeetSDK.conference.leave();
            console.log("Getting Started: Conference has ended.");
        } catch (error) {
            console.error(error);
        }
    }
    
    initializeToken = async (token) => {
    
        VoxeetSDK.initializeToken(token, () => new Promise((resolve) => resolve(token)));
        console.log("Step 1: Web SDK initialized.");
        return token;
    }
    openSession = async (participantName, avatarUrl) => {
        try {
            // await VoxeetSDK.session.close({ name: sessionName });
        await VoxeetSDK.session.open({ name: participantName, avatarUrl:avatarUrl});
        // awaitVoxeetSDK.session.updateParticipantInfo({ name: 'participantName', avatarUrl:"avatarUrl"})
        this.handleConferenceFlow()
        
        console.log("Step 2: Session opened.", participantName, avatarUrl);
        } catch (error) {
            console.log("Error: Make sure you have a valid Dolby.io Client Token", error);
        }
    }
   
    createAndJoinConference = async (conferenceAlias, participantName, avatarUrl, position) => {
        if (!VoxeetSDK.session.isOpen()) { await this.openSession(participantName, avatarUrl); };
        
        const conferenceOptions = { 

            alias: conferenceAlias,
            params:{
                audioOnly:true,
                spatialAudioStyle:"SHARED"
            }
            // params:{
            //     audioOnly:true,
            // }
        }
        const joinOptions = {
            constraints: { audio: true, video: false },
            spatialAudio:true
        };

        try {
        const conference = await VoxeetSDK.conference.create(conferenceOptions);
        await VoxeetSDK.conference.join(conference, joinOptions);
        console.log(`Step 3: Conference '${conferenceAlias}' created and joined.`);
        console.log(position, "position")
        let forward = {x:1,y:0,z:0}
        let right  = {x:0,y:1,z:0}
        let up = {x:0,y:0,z:1}
        let scale = {x:100,y:100,z:100}
        VoxeetSDK.conference.setSpatialEnvironment(scale,forward, up, right)
        VoxeetSDK.conference.setSpatialPosition(VoxeetSDK.session.participant, position)
        } catch (error) {
            console.error(error);
        }
    };
    setVolume = (volume)=>{
        if(VoxeetSDK.session.isOpen())
            VoxeetSDK.audio.remote.setOutputVolume(volume);

    }
    setMicInput = async (isOn)=>{
        console.log(isOn)
        if(VoxeetSDK.session.isOpen()){
            if(isOn){
                await VoxeetSDK.audio.local.start();
            }else{
                await VoxeetSDK.audio.local.stop();
                
            }
        }
       
        // VoxeetSDK.conference.mute(VoxeetSDK.session.participant, isMute);
    }
    setSpatialPosition = (position)=>{
        console.log("position -- ",position)
        if(VoxeetSDK.session.isOpen()){
            console.log("position -- ",position)
            VoxeetSDK.conference.setSpatialPosition(VoxeetSDK.session.participant, position)
        }
           
    }
    setSpatialDirection = (direction)=>{
        console.log("direction -- ",direction)

        if(VoxeetSDK.session.isOpen()){
            console.log("direction -- ",direction)
            VoxeetSDK.conference.setSpatialDirection(VoxeetSDK.session.participant, direction)
        }
            
    }
    getParticpants=()=>{
        var participantList = document.getElementById("participants-list");
        participantList.innerHTML = "";
        VoxeetSDK.conference.participants.forEach((participant, index)=>{
            console.log(participant)
            let participantInfo = participant.info;
            let listItem = document.createElement("li");
            listItem.innerHTML = `
            <img src="${participantInfo.avatarUrl.replace('.glb', '.png')}" alt="Profile Picture">
            <div class="name">${participantInfo.name}</div>
            
            `;
            participantList.appendChild(listItem);
              
        })
    }

    handleConferenceFlow = () => {
console.log("handleConferenceFlow")
        VoxeetSDK.conference.on('streamAdded', (participant, stream) => {
            if (stream.type === 'Camera') {
                // shareVideo(participant, stream);
            }
            this.getParticpants();
        });
        
        VoxeetSDK.conference.on('streamUpdated', (participant, stream) => {
            if (stream.type === 'Camera' && stream.getVideoTracks().length) {
                // shareVideo(participant, stream);
            }
        });

        VoxeetSDK.conference.on('streamRemoved', (participant, stream) => {
            const videoNode = document.getElementById(`video-${participant.id}`);
            if (videoNode) {
                videoNode.parentNode.removeChild(videoNode);
            }
            this.getParticpants();
        });

        VoxeetSDK.conference.on("left", async () => {
            await VoxeetSDK.session.close();
        });

        VoxeetSDK.command.on("received", (participant, message) => {
            
        });

    }

}