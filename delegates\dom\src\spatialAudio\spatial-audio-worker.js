var createAudioEngine=(()=>{var Xe="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0;return function(e){e=e||{},(m=(m="function"==typeof AudioWorkletGlobalScope?globalThis.Module:m)||(void 0!==e?e:{})).ready=new Promise(function(e,t){H=e,B=t});var m,H,B,j,i,t=Object.assign({},m),M="object"==typeof window,o="function"==typeof importScripts,n="",u=((M||o)&&(o?n=self.location.href:"undefined"!=typeof document&&document.currentScript&&(n=document.currentScript.src),n=0!==(n=Xe?Xe:n).indexOf("blob:")?n.substr(0,n.replace(/[?#].*/,"").lastIndexOf("/")+1):"",o&&(j=e=>{var t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)})),m.printErr||console.warn.bind(console));Object.assign(m,t),m.wasmBinary&&(i=m.wasmBinary),m.noExitRuntime;"object"!=typeof WebAssembly&&g("no native wasm support detected");var G,$=!1,L="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;var V,s,d,l,f,c,h,x,D,N="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0;function J(e,t){for(var n=e>>1,r=n+t/2;!(r<=n)&&f[n];)++n;if(32<(n<<=1)-e&&N)return N.decode(d.subarray(e,n));for(n="",r=0;!(t/2<=r);++r){var a=l[e+2*r>>1];if(0==a)break;n+=String.fromCharCode(a)}return n}function K(e,t,n){if((n=void 0===n?2147483647:n)<2)return 0;var r=t;n=(n-=2)<2*e.length?n/2:e.length;for(var a=0;a<n;++a)l[t>>1]=e.charCodeAt(a),t+=2;return l[t>>1]=0,t-r}function q(e){return 2*e.length}function X(e,t){for(var n=0,r="";!(t/4<=n);){var a=c[e+4*n>>2];if(0==a)break;++n,65536<=a?(a-=65536,r+=String.fromCharCode(55296|a>>10,56320|1023&a)):r+=String.fromCharCode(a)}return r}function Z(e,t,n){if((n=void 0===n?2147483647:n)<4)return 0;var r=t;n=r+n-4;for(var a=0;a<e.length;++a){var i=e.charCodeAt(a);if(55296<=i&&i<=57343&&(i=65536+((1023&i)<<10)|1023&e.charCodeAt(++a)),c[t>>2]=i,n<(t+=4)+4)break}return c[t>>2]=0,t-r}function Y(e){for(var t=0,n=0;n<e.length;++n){var r=e.charCodeAt(n);55296<=r&&r<=57343&&++n,t+=4}return t}function Q(){var e=G.buffer;V=e,m.HEAP8=s=new Int8Array(e),m.HEAP16=l=new Int16Array(e),m.HEAP32=c=new Int32Array(e),m.HEAPU8=d=new Uint8Array(e),m.HEAPU16=f=new Uint16Array(e),m.HEAPU32=h=new Uint32Array(e),m.HEAPF32=x=new Float32Array(e),m.HEAPF64=D=new Float64Array(e)}var ee,te=[],ne=[],re=[];var p,y=0,ae=null,v=null;function g(e){throw m.onAbort&&m.onAbort(e),u(e="Aborted("+e+")"),$=!0,e=new WebAssembly.RuntimeError(e+". Build with -s ASSERTIONS=1 for more info."),B(e),e}function ie(){return p.startsWith("data:application/octet-stream;base64,")}function oe(){var e=p;try{if(e==p&&i)return new Uint8Array(i);if(j)return j(e);throw"both async and sync fetching of the wasm failed"}catch(e){g(e)}}function ue(e){for(;0<e.length;){var t,n=e.shift();"function"==typeof n?n(m):"number"==typeof(t=n.sa)?void 0===n.Y?b(t)():b(t)(n.Y):t(void 0===n.Y?null:n.Y)}}m.preloadedImages={},m.preloadedAudios={},p="audio_spatializer.wasm",ie()||(t=p,p=m.locateFile?m.locateFile(t,n):n+t);var r=[];function b(e){var t=r[e];return t||(e>=r.length&&(r.length=e+1),r[e]=t=ee.get(e)),t}function se(e){this.H=e-16,this.ha=function(e){c[this.H+4>>2]=e},this.ea=function(e){c[this.H+8>>2]=e},this.fa=function(){c[this.H>>2]=0},this.da=function(){s[this.H+12>>0]=0},this.ga=function(){s[this.H+13>>0]=0},this.ca=function(e,t){this.ha(e),this.ea(t),this.fa(),this.da(),this.ga()}}function w(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+e)}}var le=void 0;function F(e){for(var t="";d[e];)t+=le[d[e++]];return t}var A={},S={},ce={};function fe(e){if(void 0===e)return"_unknown";var t=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return 48<=t&&t<=57?"_"+e:e}function pe(e,t){return e=fe(e),function(){return t.apply(this,arguments)}}function de(t){var e=Error,n=pe(t,function(e){this.name=t,this.message=e,void 0!==(e=Error(e).stack)&&(this.stack=this.toString()+"\n"+e.replace(/^Error(:[^\n]*)?\n/,""))});return n.prototype=Object.create(e.prototype),(n.prototype.constructor=n).prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},n}var T=void 0;function O(e){throw new T(e)}var he=void 0;function me(e){throw new he(e)}function k(n,t,r){function a(e){(e=r(e)).length!==n.length&&me("Mismatched type converter count");for(var t=0;t<n.length;++t)C(n[t],e[t])}n.forEach(function(e){ce[e]=t});var i=Array(t.length),o=[],u=0;t.forEach(function(e,t){S.hasOwnProperty(e)?i[t]=S[e]:(o.push(e),A.hasOwnProperty(e)||(A[e]=[]),A[e].push(function(){i[t]=S[e],++u===o.length&&a(i)}))}),0===o.length&&a(i)}function C(e,t,n={}){if(!("argPackAdvance"in t))throw new TypeError("registerType registeredInstance requires argPackAdvance");var r=t.name;if(e||O('type "'+r+'" must have a positive integer typeid pointer'),S.hasOwnProperty(e)){if(n.ma)return;O("Cannot register type '"+r+"' twice")}S[e]=t,delete ce[e],A.hasOwnProperty(e)&&(t=A[e],delete A[e],t.forEach(function(e){e()}))}function ye(e){O(e.F.I.G.name+" instance already deleted")}var ve=!1;function ge(){}function be(e){--e.count.value,0===e.count.value&&(e.J?e.K.R(e.J):e.I.G.R(e.H))}var we={},a=[];function Fe(){for(;a.length;){var e=a.pop();e.F.S=!1,e.delete()}}var I=void 0,P={};function Ae(e,t){return t.I&&t.H||me("makeClassHandle requires ptr and ptrType"),!!t.K!=!!t.J&&me("Both smartPtrType and smartPtr must be specified"),t.count={value:1},Se(Object.create(e,{F:{value:t}}))}function Se(e){return"undefined"==typeof FinalizationRegistry?(Se=e=>e,e):(ve=new FinalizationRegistry(e=>{be(e.F)}),ge=e=>{ve.unregister(e)},(Se=e=>{var t=e.F;return t.J&&ve.register(e,{F:t},e),e})(e))}function E(){}function Te(e,t,n){var r;void 0===e[t].N&&(r=e[t],e[t]=function(){return e[t].N.hasOwnProperty(arguments.length)||O("Function '"+n+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+e[t].N+")!"),e[t].N[arguments.length].apply(this,arguments)},e[t].N=[],e[t].N[r.V]=r)}function Oe(e,t){m.hasOwnProperty(e)?(O("Cannot register public name '"+e+"' twice"),Te(m,e,e),m.hasOwnProperty(void 0)&&O("Cannot register multiple overloads of a function with the same number of arguments (undefined)!"),m[e].N[void 0]=t):m[e]=t}function ke(e,t,n,r,a,i,o,u){this.name=e,this.constructor=t,this.O=n,this.R=r,this.L=a,this.ka=i,this.U=o,this.ja=u,this.oa=[]}function Ce(e,t,n){for(;t!==n;)t.U||O("Expected null or instance of "+n.name+", got an instance of "+t.name),e=t.U(e),t=t.L;return e}function Ie(e,t){return null===t?(this.Z&&O("null is not a valid "+this.name),0):(t.F||O('Cannot pass "'+Le(t)+'" as a '+this.name),t.F.H||O("Cannot pass deleted object as a pointer of type "+this.name),Ce(t.F.H,t.F.I.G,this.G))}function Pe(e,t){var n,r;if(null===t)return this.Z&&O("null is not a valid "+this.name),this.X?(r=this.pa(),null!==e&&e.push(this.R,r),r):0;if(t.F||O('Cannot pass "'+Le(t)+'" as a '+this.name),t.F.H||O("Cannot pass deleted object as a pointer of type "+this.name),!this.W&&t.F.I.W&&O("Cannot convert argument of type "+(t.F.K||t.F.I).name+" to parameter type "+this.name),r=Ce(t.F.H,t.F.I.G,this.G),this.X)switch(void 0===t.F.J&&O("Passing raw pointer to smart pointer is illegal"),this.ra){case 0:t.F.K===this?r=t.F.J:O("Cannot convert argument of type "+(t.F.K||t.F.I).name+" to parameter type "+this.name);break;case 1:r=t.F.J;break;case 2:t.F.K===this?r=t.F.J:(n=t.clone(),r=this.qa(r,Ge(function(){n.delete()})),null!==e&&e.push(this.R,r));break;default:O("Unsupporting sharing policy")}return r}function Ee(e,t){return null===t?(this.Z&&O("null is not a valid "+this.name),0):(t.F||O('Cannot pass "'+Le(t)+'" as a '+this.name),t.F.H||O("Cannot pass deleted object as a pointer of type "+this.name),t.F.I.W&&O("Cannot convert argument of type "+t.F.I.name+" to parameter type "+this.name),Ce(t.F.H,t.F.I.G,this.G))}function We(e){return this.fromWireType(h[e>>2])}function W(e,t,n,r){this.name=e,this.G=t,this.Z=n,this.W=r,this.X=!1,(this.R=this.qa=this.pa=this.ba=this.ra=this.na=void 0)!==t.L?this.toWireType=Pe:(this.toWireType=r?Ie:Ee,this.M=null)}function R(e,t){var n,r,a,i=(e=F(e)).includes("j")?(n=e,r=t,a=[],function(){var e;return a.length=0,Object.assign(a,arguments),e=n.includes("j")?(e=m["dynCall_"+n],a&&a.length?e.apply(null,[r].concat(a)):e.call(null,r)):b(r).apply(null,a)}):b(t);return"function"!=typeof i&&O("unknown function pointer with signature "+e+": "+t),i}var Re=void 0;function ze(e){var t=F(e=Ke(e));return U(e),t}function z(e,t){var n=[],r={};throw t.forEach(function e(t){r[t]||S[t]||(ce[t]?ce[t].forEach(e):(n.push(t),r[t]=!0))}),new Re(e+": "+n.map(ze).join([", "]))}function _e(e,t){for(var n=[],r=0;r<e;r++)n.push(c[(t>>2)+r]);return n}function Ue(e){for(;e.length;){var t=e.pop();e.pop()(t)}}function He(a,i,e,o,u){var t=i.length,s=(t<2&&O("argTypes array size mismatch! Must at least get return value and 'this' types!"),null!==i[1]&&null!==e),l=!1;for(e=1;e<i.length;++e)if(null!==i[e]&&void 0===i[e].M){l=!0;break}var c="void"!==i[0].name,f=t-2,p=Array(f),d=[],h=[];return function(){var e;arguments.length!==f&&O("function "+a+" called with "+arguments.length+" arguments, expected "+f+" args!"),h.length=0,d.length=s?2:1,d[0]=u,s&&(e=i[1].toWireType(h,this),d[1]=e);for(var t=0;t<f;++t)p[t]=i[t+2].toWireType(h,arguments[t]),d.push(p[t]);if(t=o.apply(null,d),l)Ue(h);else for(var n=s?1:2;n<i.length;n++){var r=1===n?e:p[n-2];null!==i[n].M&&i[n].M(r)}return e=c?i[0].fromWireType(t):void 0}}function Be(e,t,n){return e instanceof Object||O(n+' with invalid "this": '+e),e instanceof t.G.constructor||O(n+' incompatible with "this" of type '+e.constructor.name),e.F.H||O("cannot call emscripten binding method "+n+" on deleted object"),Ce(e.F.H,e.F.I.G,t.G)}var je=[],_=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function Me(e){4<e&&0==--_[e].$&&(_[e]=void 0,je.push(e))}function Ge(e){switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var t=je.length?je.pop():_.length;return _[t]={$:1,value:e},t}}function $e(e,t){var n=S[e];return void 0===n&&O(t+" has unknown type "+ze(e)),n}function Le(e){if(null===e)return"null";var t=typeof e;return"object"==t||"array"==t||"function"==t?e.toString():""+e}for(var Ve=Array(256),xe=0;xe<256;++xe)Ve[xe]=String.fromCharCode(xe);le=Ve,T=m.BindingError=de("BindingError"),he=m.InternalError=de("InternalError"),E.prototype.isAliasOf=function(e){if(!(this instanceof E&&e instanceof E))return!1;var t=this.F.I.G,n=this.F.H,r=e.F.I.G;for(e=e.F.H;t.L;)n=t.U(n),t=t.L;for(;r.L;)e=r.U(e),r=r.L;return t===r&&n===e},E.prototype.clone=function(){if(this.F.H||ye(this),this.F.T)return this.F.count.value+=1,this;var e=Se,t=Object,n=t.create,r=Object.getPrototypeOf(this),a=this.F;return(e=e(n.call(t,r,{F:{value:{count:a.count,S:a.S,T:a.T,H:a.H,I:a.I,J:a.J,K:a.K}}}))).F.count.value+=1,e.F.S=!1,e},E.prototype.delete=function(){this.F.H||ye(this),this.F.S&&!this.F.T&&O("Object already scheduled for deletion"),ge(this),be(this.F),this.F.T||(this.F.J=void 0,this.F.H=void 0)},E.prototype.isDeleted=function(){return!this.F.H},E.prototype.deleteLater=function(){return this.F.H||ye(this),this.F.S&&!this.F.T&&O("Object already scheduled for deletion"),a.push(this),1===a.length&&I&&I(Fe),this.F.S=!0,this},m.getInheritedInstanceCount=function(){return Object.keys(P).length},m.getLiveInheritedInstances=function(){var e,t=[];for(e in P)P.hasOwnProperty(e)&&t.push(P[e]);return t},m.flushPendingDeletes=Fe,m.setDelayFunction=function(e){I=e,a.length&&I&&I(Fe)},W.prototype.la=function(e){return e=this.ba?this.ba(e):e},W.prototype.aa=function(e){this.R&&this.R(e)},W.prototype.argPackAdvance=8,W.prototype.readValueFromPointer=We,W.prototype.deleteObject=function(e){null!==e&&e.delete()},W.prototype.fromWireType=function(e){function t(){return this.X?Ae(this.G.O,{I:this.na,H:n,K:this,J:e}):Ae(this.G.O,{I:this,H:e})}var n=this.la(e);if(!n)return this.aa(e),null;if(void 0!==(r=function(e,t){for(void 0===t&&O("ptr should not be undefined");e.L;)t=e.U(t),e=e.L;return P[t]}(this.G,n)))return 0===r.F.count.value?(r.F.H=n,r.F.J=e,r.clone()):(r=r.clone(),this.aa(e),r);if(r=this.G.ka(n),!(r=we[r]))return t.call(this);var r=this.W?r.ia:r.pointerType,a=function e(t,n,r){return n===r?t:void 0===r.L||null===(t=e(t,n,r.L))?null:r.ja(t)}(n,this.G,r.G);return null===a?t.call(this):this.X?Ae(r.G.O,{I:r,H:a,K:this,J:e}):Ae(r.G.O,{I:r,H:a})},Re=m.UnboundTypeError=de("UnboundTypeError"),m.count_emval_handles=function(){for(var e=0,t=5;t<_.length;++t)void 0!==_[t]&&++e;return e},m.get_first_emval=function(){for(var e=5;e<_.length;++e)if(void 0!==_[e])return _[e];return null};var De,Ne={e:function(e){return Je(e+16)+16},d:function(e,t,n){throw new se(e).ca(t,n),e},r:function(){},v:function(e,n,r,a,i){var o=w(r);C(e,{name:n=F(n),fromWireType:function(e){return!!e},toWireType:function(e,t){return t?a:i},argPackAdvance:8,readValueFromPointer:function(e){if(1===r)var t=s;else if(2===r)t=l;else{if(4!==r)throw new TypeError("Unknown boolean type size: "+n);t=c}return this.fromWireType(t[e>>o])},M:null})},k:function(u,e,t,s,n,l,r,c,a,f,p,i,d){p=F(p),l=R(n,l),c=c&&R(r,c),f=f&&R(a,f),d=R(i,d);var h=fe(p);Oe(h,function(){z("Cannot construct "+p+" due to unbound types",[s])}),k([u,e,t],s?[s]:[],function(e){e=e[0],i=s?(a=e.G).O:E.prototype,e=pe(h,function(){if(Object.getPrototypeOf(this)!==n)throw new T("Use 'new' to construct "+p);if(void 0===r.P)throw new T(p+" has no accessible constructor");var e=r.P[arguments.length];if(void 0===e)throw new T("Tried to invoke ctor of "+p+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(r.P).toString()+") parameters instead!");return e.apply(this,arguments)});var t,n=Object.create(i,{constructor:{value:e}}),r=(e.prototype=n,new ke(p,e,n,d,a,l,c,f)),a=new W(p,r,!0,!1),i=new W(p+"*",r,!1,!1),o=new W(p+" const*",r,!1,!0);return we[u]={pointerType:i,ia:o},t=h,e=e,m.hasOwnProperty(t)||me("Replacing nonexistant public symbol"),m[t]=e,m[t].V=void 0,[a,i,o]})},j:function(e,r,t,n,a,i){0<r||g(void 0);var o=_e(r,t);a=R(n,a),k([],[e],function(t){var n="constructor "+(t=t[0]).name;if(void 0===t.G.P&&(t.G.P=[]),void 0!==t.G.P[r-1])throw new T("Cannot register multiple constructors with identical number of parameters ("+(r-1)+") for class '"+t.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return t.G.P[r-1]=()=>{z("Cannot construct "+t.name+" due to unbound types",o)},k([],o,function(e){return e.splice(1,0,null),t.G.P[r-1]=He(n,e,null,a,i),[]}),[]})},b:function(e,i,o,t,n,u,s,l){var c=_e(o,t);i=F(i),u=R(n,u),k([],[e],function(t){function e(){z("Cannot call "+n+" due to unbound types",c)}var n=(t=t[0]).name+"."+i,r=(i.startsWith("@@")&&(i=Symbol[i.substring(2)]),l&&t.G.oa.push(i),t.G.O),a=r[i];return void 0===a||void 0===a.N&&a.className!==t.name&&a.V===o-2?(e.V=o-2,e.className=t.name,r[i]=e):(Te(r,i,n),r[i].N[o-2]=e),k([],c,function(e){return e=He(n,e,t,u,s),void 0===r[i].N?(e.V=o-2,r[i]=e):r[i].N[o-2]=e,[]}),[]})},i:function(e,o,t,n,u,s,r,l,c,f){o=F(o),u=R(n,u),k([],[e],function(a){var i=(a=a[0]).name+"."+o,e={get:function(){z("Cannot access "+i+" due to unbound types",[t,r])},enumerable:!0,configurable:!0};return e.set=c?()=>{z("Cannot access "+i+" due to unbound types",[t,r])}:()=>{O(i+" is a read-only property")},Object.defineProperty(a.G.O,o,e),k([],c?[t,r]:[t],function(e){var r,t=e[0],n={get:function(){var e=Be(this,a,i+" getter");return t.fromWireType(u(s,e))},enumerable:!0};return c&&(c=R(l,c),r=e[1],n.set=function(e){var t=Be(this,a,i+" setter"),n=[];c(f,t,r.toWireType(n,e)),Ue(n)}),Object.defineProperty(a.G.O,o,n),[]}),[]})},u:function(e,t){C(e,{name:t=F(t),fromWireType:function(e){e||O("Cannot use deleted val. handle = "+e);var t=_[e].value;return Me(e),t},toWireType:function(e,t){return Ge(t)},argPackAdvance:8,readValueFromPointer:We,M:null})},n:function(e,t,n,r){function a(){}n=w(n),t=F(t),a.values={},C(e,{name:t,constructor:a,fromWireType:function(e){return this.constructor.values[e]},toWireType:function(e,t){return t.value},argPackAdvance:8,readValueFromPointer:function(e,t,n){switch(t){case 0:return function(e){return this.fromWireType((n?s:d)[e])};case 1:return function(e){return this.fromWireType((n?l:f)[e>>1])};case 2:return function(e){return this.fromWireType((n?c:h)[e>>2])};default:throw new TypeError("Unknown integer type: "+e)}}(t,n,r),M:null}),Oe(t,a)},h:function(e,t,n){var r=$e(e,"enum");t=F(t),e=r.constructor,r=Object.create(r.constructor.prototype,{value:{value:n},constructor:{value:pe(r.name+"_"+t,function(){})}}),e.values[n]=r,e[t]=r},l:function(e,t,n){n=w(n),C(e,{name:t=F(t),fromWireType:function(e){return e},toWireType:function(e,t){return t},argPackAdvance:8,readValueFromPointer:function(e,t){switch(t){case 2:return function(e){return this.fromWireType(x[e>>2])};case 3:return function(e){return this.fromWireType(D[e>>3])};default:throw new TypeError("Unknown float type: "+e)}}(t,n),M:null})},c:function(e,t,n,r,a){t=F(t),-1===a&&(a=4294967295),a=w(n);var i,o=e=>e;0===r&&(i=32-8*n,o=e=>e<<i>>>i),n=t.includes("unsigned")?function(e,t){return t>>>0}:function(e,t){return t},C(e,{name:t,fromWireType:o,toWireType:n,argPackAdvance:8,readValueFromPointer:function(e,t,n){switch(t){case 0:return n?function(e){return s[e]}:function(e){return d[e]};case 1:return n?function(e){return l[e>>1]}:function(e){return f[e>>1]};case 2:return n?function(e){return c[e>>2]}:function(e){return h[e>>2]};default:throw new TypeError("Unknown integer type: "+e)}}(t,a,0!==r),M:null})},a:function(e,t,n){function r(e){var t=h;return new a(V,t[(e>>=2)+1],t[e])}var a=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][t];C(e,{name:n=F(n),fromWireType:r,argPackAdvance:8,readValueFromPointer:r},{ma:!0})},m:function(e,t){var p="std::string"===(t=F(t));C(e,{name:t,fromWireType:function(e){var t=h[e>>2];if(p)for(var n=e+4,r=0;r<=t;++r){var a=e+4+r;if(r==t||0==d[a]){if(n){for(var i=n,o=d,u=i+(a-n),n=i;o[n]&&!(u<=n);)++n;if(16<n-i&&o.subarray&&L)i=L.decode(o.subarray(i,n));else{for(u="";i<n;){var s,l,c=o[i++];128&c?(s=63&o[i++],192==(224&c)?u+=String.fromCharCode((31&c)<<6|s):(l=63&o[i++],(c=224==(240&c)?(15&c)<<12|s<<6|l:(7&c)<<18|s<<12|l<<6|63&o[i++])<65536?u+=String.fromCharCode(c):(c-=65536,u+=String.fromCharCode(55296|c>>10,56320|1023&c)))):u+=String.fromCharCode(c)}i=u}}else i="";var f=void 0===f?i:f+String.fromCharCode(0)+i;n=a+1}}else{for(f=Array(t),r=0;r<t;++r)f[r]=String.fromCharCode(d[e+4+r]);f=f.join("")}return U(e),f},toWireType:function(e,r){var t="string"==typeof(r=r instanceof ArrayBuffer?new Uint8Array(r):r),n=(t||r instanceof Uint8Array||r instanceof Uint8ClampedArray||r instanceof Int8Array||O("Cannot pass non-string to std::string"),(p&&t?()=>{for(var e=0,t=0;t<r.length;++t){var n=r.charCodeAt(t);(n=55296<=n&&n<=57343?65536+((1023&n)<<10)|1023&r.charCodeAt(++t):n)<=127?++e:e=n<=2047?e+2:n<=65535?e+3:e+4}return e}:()=>r.length)()),a=Je(4+n+1);if(h[a>>2]=n,p&&t){var i=r;var o=a+4;var u=n+1;var s=d;if(0<u){u=o+u-1;for(var l=0;l<i.length;++l){var c=i.charCodeAt(l);if((c=55296<=c&&c<=57343?65536+((1023&c)<<10)|1023&i.charCodeAt(++l):c)<=127){if(u<=o)break;s[o++]=c}else{if(c<=2047){if(u<=o+1)break;s[o++]=192|c>>6}else{if(c<=65535){if(u<=o+2)break;s[o++]=224|c>>12}else{if(u<=o+3)break;s[o++]=240|c>>18,s[o++]=128|c>>12&63}s[o++]=128|c>>6&63}s[o++]=128|63&c}}s[o]=0}}else if(t)for(t=0;t<n;++t){var f=r.charCodeAt(t);255<f&&(U(a),O("String has UTF-16 code units that do not fit in 8 bits")),d[a+4+t]=f}else for(t=0;t<n;++t)d[a+4+t]=r[t];return null!==e&&e.push(U,a),a},argPackAdvance:8,readValueFromPointer:We,M:function(e){U(e)}})},g:function(e,u,a){var s,i,o,l,c;a=F(a),2===u?(s=J,i=K,o=q,l=()=>f,c=1):4===u&&(s=X,i=Z,o=Y,l=()=>h,c=2),C(e,{name:a,fromWireType:function(e){for(var t,n=h[e>>2],r=l(),a=e+4,i=0;i<=n;++i){var o=e+4+i*u;i!=n&&0!=r[o>>c]||(a=s(a,o-a),void 0===t?t=a:t=t+String.fromCharCode(0)+a,a=o+u)}return U(e),t},toWireType:function(e,t){"string"!=typeof t&&O("Cannot pass non-string to C++ string type "+a);var n=o(t),r=Je(4+n+u);return h[r>>2]=n>>c,i(t,r+4,n+u),null!==e&&e.push(U,r),r},argPackAdvance:8,readValueFromPointer:We,M:function(e){U(e)}})},w:function(e,t){C(e,{ta:!0,name:t=F(t),argPackAdvance:0,fromWireType:function(){},toWireType:function(){}})},o:Me,p:function(e){4<e&&(_[e].$+=1)},q:function(e,t){return Ge(e=(e=$e(e,"_emval_take_value")).readValueFromPointer(t))},s:function(){g("")},t:function(e,t,n){d.copyWithin(e,t,t+n)},f:function(e){var t=d.length;if(2147483648<(e>>>=0))return!1;for(var n=1;n<=4;n*=2){var r=t*(1+.2/n),r=Math.min(r,e+100663296),a=Math;r=Math.max(e,r),a=a.min.call(a,2147483648,r+(65536-r%65536)%65536);e:{try{G.grow(a-V.byteLength+65535>>>16),Q();var i=1;break e}catch(e){}i=void 0}if(i)return!0}return!1}},Je=(!function(){function t(e){m.asm=e.exports,G=m.asm.x,Q(),ee=m.asm.D,ne.unshift(m.asm.y),y--,m.monitorRunDependencies&&m.monitorRunDependencies(y),0==y&&(null!==ae&&(clearInterval(ae),ae=null),v&&(e=v,v=null,e()))}function n(e){t(e.instance)}function r(e){return(i||!M&&!o||"function"!=typeof fetch?Promise.resolve().then(oe):fetch(p,{credentials:"same-origin"}).then(function(e){if(e.ok)return e.arrayBuffer();throw"failed to load wasm binary file at '"+p+"'"}).catch(oe)).then(function(e){return WebAssembly.instantiate(e,a)}).then(function(e){return e}).then(e,function(e){u("failed to asynchronously prepare wasm: "+e),g(e)})}var a={a:Ne};if(y++,m.monitorRunDependencies&&m.monitorRunDependencies(y),m.instantiateWasm)try{return m.instantiateWasm(a,t)}catch(e){return u("Module.instantiateWasm callback failed with error: "+e)}(i||"function"!=typeof WebAssembly.instantiateStreaming||ie()||"function"!=typeof fetch?r(n):fetch(p,{credentials:"same-origin"}).then(function(e){return WebAssembly.instantiateStreaming(e,a).then(n,function(e){return u("wasm streaming compile failed: "+e),u("falling back to ArrayBuffer instantiation"),r(n)})})).catch(B)}(),m.___wasm_call_ctors=function(){return(m.___wasm_call_ctors=m.asm.y).apply(null,arguments)},m._malloc=function(){return(Je=m._malloc=m.asm.z).apply(null,arguments)}),U=m._free=function(){return(U=m._free=m.asm.A).apply(null,arguments)},Ke=m.___getTypeName=function(){return(Ke=m.___getTypeName=m.asm.B).apply(null,arguments)};function qe(){function e(){if(!De&&(De=!0,m.calledRun=!0,!$)){if(ue(ne),H(m),m.onRuntimeInitialized&&m.onRuntimeInitialized(),m.postRun)for("function"==typeof m.postRun&&(m.postRun=[m.postRun]);m.postRun.length;){var e=m.postRun.shift();re.unshift(e)}ue(re)}}if(!(0<y)){if(m.preRun)for("function"==typeof m.preRun&&(m.preRun=[m.preRun]);m.preRun.length;)t=void 0,t=m.preRun.shift(),te.unshift(t);ue(te),0<y||(m.setStatus?(m.setStatus("Running..."),setTimeout(function(){setTimeout(function(){m.setStatus("")},1),e()},1)):e())}var t}if(m.___embind_register_native_and_builtin_types=function(){return(m.___embind_register_native_and_builtin_types=m.asm.C).apply(null,arguments)},v=function e(){De||qe(),De||(v=e)},m.run=qe,m.preInit)for("function"==typeof m.preInit&&(m.preInit=[m.preInit]);0<m.preInit.length;)m.preInit.pop()();return qe(),e.ready}})();"object"==typeof exports&&"object"==typeof module?module.exports=createAudioEngine:"function"==typeof define&&define.amd?define([],function(){return createAudioEngine}):"object"==typeof exports&&(exports.createAudioEngine=createAudioEngine),"function"==typeof AudioWorkletGlobalScope&&(globalThis.createAudioEngine=createAudioEngine);const EventTypes=["Init","Start","Stop","UpdateSpatialAzimuth","UpdateSpatialElevation","UpdateSpatialOrientation","UpdateSpatialDistance","UpdateSpatialBlur","UpdateSpatialAirAbsorb","UpdateSpatialAttenuation"],procEmptyItem={readableStream:null,writableStream:null,controller:null,azimuth:0,elevation:0,audioChunkSampleRate:-12345,wasmSpatializer:null,wasmInBuffer:null,wasmOutBuffer:null};let wasmModule=null,wasmModuleLoaded=!1;const processorTable=new Map,isEngineInitialized=(e,t)=>!!e.wasmSpatializer||(console.warn(`Cannot ${t} as wasmSpatializer (id = ${getKeyByValue(processorTable,e)}) is not constructed.`),!1),getProcessor=e=>{if(e)return processorTable.has(e)||processorTable.set(e,Object.assign({},procEmptyItem)),processorTable.get(e)},getKeyByValue=(t,n)=>[...t.keys()].find(e=>t.get(e)===n);self.addEventListener("message",async e=>{var n=e.data.type;if(EventTypes.includes(n)){console.info(`spatial audio worker(processor-${e.data.id}) receive message from main: '${n}'`);const u=getProcessor(e.data.id);switch(n){case"Init":if(!wasmModule)try{wasmModule=await createAudioEngine(),wasmModuleLoaded=!0,console.info("spatial audio algorithm engine loading successful !!!")}catch(e){throw new Error("spatial audio algorithm engine loading failed: "+e)}break;case"Start":Object.assign(u,{readableStream:e.data.readableStream,writableStream:e.data.writableStream});var r=new TransformStream({transform:(t,e)=>{if(!1===wasmModuleLoaded)console.debug("Bypass spatial audio algorithm engine as loading not finished yet"),e.enqueue(t);else{u.audioChunkSampleRate!==t.sampleRate&&(console.info(`Input AudioData Chunk: format = ${t.format}, sampleRate = ${t.sampleRate}, numberOfFrames = ${t.numberOfFrames}, numberOfChannels = `+t.numberOfChannels),u.audioChunkSampleRate=t.sampleRate,u.wasmSpatializer&&u.wasmSpatializer.delete(),u.wasmInBuffer&&u.wasmInBuffer.delete(),u.wasmOutBuffer&&u.wasmOutBuffer.delete(),u.wasmSpatializer=new wasmModule.AudioSpatializer(t.sampleRate,t.numberOfFrames),u.wasmInBuffer=new wasmModule.AudioBuffer(1,t.numberOfFrames),u.wasmOutBuffer=new wasmModule.AudioBuffer(2,t.numberOfFrames));var n="f32-planar";const i=new Float32Array(t.numberOfFrames*t.numberOfChannels);for(let e=0;e<t.numberOfChannels;e++){var r=t.numberOfFrames*e;t.copyTo(i.subarray(r,r+t.numberOfFrames),{planeIndex:e,format:n})}u.wasmInBuffer.getChannelData(0).set(i.subarray(0,t.numberOfFrames));try{u.wasmSpatializer.process(u.wasmInBuffer,u.wasmOutBuffer)}catch(e){return void console.error(`wasm spatializer (id = ${getKeyByValue(processorTable,u)}) process error: `+e)}const o=new Float32Array(2*t.numberOfFrames);for(let e=0;e<2;e++){var a=t.numberOfFrames*e;o.set(u.wasmOutBuffer.getChannelData(e),a)}e.enqueue(new AudioData({format:n,sampleRate:t.sampleRate,numberOfFrames:t.numberOfFrames,numberOfChannels:2,timestamp:t.timestamp,data:o}))}},start:e=>{console.info(`TransformStream.start (id = ${getKeyByValue(processorTable,u)}): `+JSON.stringify(e))},flush:e=>{console.info(`TransformStream.flush (id = ${getKeyByValue(processorTable,u)}): `+JSON.stringify(e)),e.terminate()}},{highWaterMark:1048576},{highWaterMark:1048576});u.controller||(u.controller=new AbortController);let t=u.controller.signal;u.readableStream.pipeThrough(r,{signal:t}).pipeTo(u.writableStream).catch(e=>{console.error(`Error from stream transformer (id = ${getKeyByValue(processorTable,u)}): `+e.message),t.aborted&&(u.readableStream.cancel(e),u.writableStream.abort(e))});break;case"Stop":u.controller?.abort(),u.controller=null;break;case"UpdateSpatialAzimuth":if(!isEngineInitialized(u,n))return;console.log(`worker(processor-${e.data.id}) update azimuth = `+e.data.azimuth),u.azimuth=e.data.azimuth,u.wasmSpatializer.setPosition(u.azimuth,u.elevation);break;case"UpdateSpatialElevation":if(!isEngineInitialized(u,n))return;console.log(`worker(processor-${e.data.id}) update elevation = `+e.data.elevation),u.elevation=e.data.elevation,u.wasmSpatializer.setPosition(u.azimuth,u.elevation);break;case"UpdateSpatialOrientation":if(!isEngineInitialized(u,n))return;console.log(`worker(processor-${e.data.id}) update orientation = `+e.data.orientation),u.wasmSpatializer.setOrientation(e.data.orientation);break;case"UpdateSpatialDistance":if(!isEngineInitialized(u,n))return;console.log(`worker(processor-${e.data.id}) update distance = `+e.data.distance),u.wasmSpatializer.setDistance(e.data.distance);break;case"UpdateSpatialBlur":if(!isEngineInitialized(u,n))return;console.log(`worker(processor-${e.data.id}) update blur = `+(e.data.enable?"true":"false")),e.data.enable?u.wasmSpatializer.enableFeature(wasmModule.AudioSpatializerFeature.BLUR):u.wasmSpatializer.disableFeature(wasmModule.AudioSpatializerFeature.BLUR);break;case"UpdateSpatialAirAbsorb":if(!isEngineInitialized(u,n))return;console.log(`worker(processor-${e.data.id}) update airabsorb = `+(e.data.enable?"true":"false")),e.data.enable?u.wasmSpatializer.enableFeature(wasmModule.AudioSpatializerFeature.AIRABSORB):u.wasmSpatializer.disableFeature(wasmModule.AudioSpatializerFeature.AIRABSORB);break;case"UpdateSpatialAttenuation":if(!isEngineInitialized(u,n))return;console.log(`worker(processor-${e.data.id}) update attenuation = `+e.data.factor),u.wasmSpatializer.setAttenuationFactor(e.data.factor)}postMessage({type:"on"+n,id:""+e.data.id})}else console.warn(`spatial audio worker receive unsupported message from main: '${n}'`)});