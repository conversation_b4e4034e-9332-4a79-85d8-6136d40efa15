name: Publish library package to npmjs
on:
  push:
    tags:
      - '*'
jobs:
  build:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./library
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '16.x'
          registry-url: 'https://registry.npmjs.org'
      - run: npm ci
      - run: npm run build
      - run: npm publish --access public
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
