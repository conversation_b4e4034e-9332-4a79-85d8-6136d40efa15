:root {
	/*Using colour scheme https://color.adobe.com/TD-Colors---Option-3-color-theme-10394433/*/
	--colour1: #2B3A42;
	--colour2: #242424;
	--colour3: #BDD4DE;
	--colour4: #b0b0b0;
	--colour5: #0262c3;
	--colour6: #0f0f0f;
	--colour7: #2f2f2f;
	--colour8: #151515;
	--colour9: #808080;

	--buttonFont: Arial;
	--inputFont: Arial;
}

.noselect {
	-webkit-touch-callout: none;
	/* iOS Safari */
	-webkit-user-select: none;
	/* Safari */
	-khtml-user-select: none;
	/* Konqueror HTML */
	-moz-user-select: none;
	/* Old versions of Firefox */
	-ms-user-select: none;
	/* Internet Explorer/Edge */
	user-select: none;
	/* Non-prefixed version, currently
									supported by Chrome, Edge, Opera and Firefox */
}


#playerUI {
	width: 100%;
	height: 100%;

}

canvas {
	image-rendering: crisp-edges;
	position: absolute;
}

#player {
	width: 100%;
	height: 100%;
	position: absolute;
	
}

/* UI STUFF */
#uiFeatures {
	width: 100%;
	height: 100%;
	z-index: 30;
	position: absolute;
	color: var(--colour2);
	pointer-events: none;
	overflow: hidden;
}

.spsUiTool .tooltiptext {
	visibility: hidden;
	width: auto;
	color: var(--colour4);
	text-align: center;
	border-radius: 15px;
	padding: 0px 10px;
	font-family: 'Montserrat', sans-serif;
	font-size: 0.75rem;
	letter-spacing: 0.75px;
	/* Position the tooltip */
	position: absolute;
	top: 0;
	transform: translateY(25%);
	left: 125%;
	z-index: 20;
}

.spsUiTool:hover .tooltiptext {
	visibility: visible;
	background-color: var(--colour7);
}

#connection .tooltiptext {
	top: 125%;
	transform: translateX(-25%);
	left: 0;
	z-index: 20;
	padding: 5px 10px;
}

#connection {
	position: absolute;
	bottom: 8%;
	left: 5%;
	font-family: 'Michroma', sans-serif;
	height: 3rem;
	width: 3rem;
	pointer-events: all;
}

#settings-panel .tooltiptext {
	display: block;
	top: 125%;
	transform: translateX(-50%);
	left: 0;
	z-index: 20;
	padding: 5px 10px;
	border: 3px solid var(--colour5);
	width: max-content;
}

#controls {
	position: absolute;
	top: 3%;
	left: 2%;
	font-family: 'Michroma', sans-serif;
	pointer-events: all;
	/* display: block; */
	display: none;
}

#controls>* {
	margin-bottom: 0.5rem;
	border-radius: 50%;
	display: block;
	height: 2rem;
	line-height: 1.75rem;
	padding: 0.5rem;
}

#controls #additionalinfo {
	text-align: center;
	font-family: 'Montserrat', sans-serif;
}

#fullscreen-btn {
	padding: 0.6rem !important;
}

object {
	pointer-events: none;
}

#minimizeIcon {
	display: none;
}

#settingsBtn,
#statsBtn {
	cursor: pointer;
}

#uiFeatures button {
	background-color: var(--colour7);
	border: 1px solid var(--colour7);
	color: var(--colour4);
	position: relative;
	width: 3rem;
	height: 3rem;
	padding: 0.5rem;
	text-align: center;
}

#uiFeatures button:hover {
	background-color: var(--colour3);
	border: 3px solid var(--colour3);
	transition: 0.25s ease;
	padding-left: 0.55rem;
	padding-top: 0.55rem;
}

#uiFeatures button:active {
	border: 3px solid var(--colour3);
	background-color: var(--colour7);
	padding-left: 0.55rem;
	padding-top: 0.55rem;
}

#uiFeatures img {
	width: 100%;
	height: 100%;
}

.panel-wrap {
	position: fixed;
	top: 0;
	bottom: 0;
	right: 0;
	height: 100%;
	min-width: 20vw;
	max-width: 100vw;
	transform: translateX(100%);
	transition: .3s ease-out;
	pointer-events: all;
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	overflow-y: auto;
	overflow-x: hidden;
	background-color: rgba(30, 29, 34, 0.5)
}

.panel-wrap-visible {
	transform: translateX(0%);
}

.panel {
	color: #eee;
	overflow-y: auto;
	padding: 1em;
}

#settingsHeading,
#statsHeading {
	display: inline-block;
	font-size: 2em;
	margin-block-start: 0.67em;
	margin-block-end: 0.67em;
	margin-inline-start: 0px;
	margin-inline-end: 0px;
	position: relative;
	padding: 0 0 0 2rem;
}

#settingsClose,
#statsClose {
	margin: 0.5rem;
	padding-top: 0.5rem;
	padding-bottom: 0.5rem;
	padding-right: 0.5rem;
	font-size: 2em;
	float: right;
}

#settingsClose:after,
#statsClose:after {
	padding-left: 0.5rem;
	display: inline-block;
	content: "\00d7";
	/* This will render the 'X' */
}

#settingsClose:hover,
#statsClose:hover {
	color: var(--colour3);
	transition: ease 0.3s;
}

#settingsContent,
#statsContent {
	margin-left: 2rem;
	margin-right: 2rem;
}

#settings-panel .tooltiptext {
	display: block;
	top: 125%;
	transform: translateX(-50%);
	left: 0;
	z-index: 20;
	padding: 5px 10px;
	border: 3px solid var(--colour5);
	width: max-content;
}

/* UI STUFF */

.setting {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	padding: 2px 10px 2px 10px;
}

.settings-text {
	font-size: 1.1em;
	color: var(--colour4);
	vertical-align: middle;
	font-weight: normal;
}

#connectOverlay,
#playOverlay,
#infoOverlay,
#errorOverlay,
#afkOverlay,
#disconnectOverlay {
	z-index: 30;
	position: absolute;
	color: var(--colour4);
	font-size: 1.8em;
	font-family: var(--inputFont);
	width: 100%;
	height: 100%;
	/* background-color: #808080; */
	background-color: transparent;
	align-items: center;
	justify-content: center;

}

/* State for element to be clickable */
.clickableState {
	align-items: center;
	justify-content: center;
	display: flex;
	cursor: pointer;
}

/* State for element to show text, this is for informational use*/
.textDisplayState {
	display: flex;
}

/* State to hide overlay, WebRTC communication is in progress and or is playing */
.hiddenState {
	display: none;
}

#playButton,
#connectButton {
	display: inline-block;
	height: auto;
	z-index: 30;
}

img#playButton {
	max-width: 241px;
	width: 10%;
}

#uiInteraction {
	position: fixed;
}

#UIInteractionButtonBoundary {
	padding: 2px;
}

#UIInteractionButton {
	cursor: pointer;
}

#hiddenInput {
	position: absolute;
	left: -10%;
	/* Although invisible, push off-screen to prevent user interaction. */
	width: 0px;
	opacity: 0;
}

#editTextButton {
	position: absolute;
	height: 40px;
	width: 40px;
}

.overlay-button {
	line-height: 1.1;
	padding: 1px 6px;
}

.btn-overlay {
	vertical-align: middle;
	display: inline-block;
}

.btn-flat {
	background: var(--colour7);
	border: 2px solid var(--colour8);
	color: var(--colour4);
	font-weight: bold;
	cursor: pointer;
	font-family: var(--buttonFont);
	font-size: 10px;
	border-radius: 5px;
	padding: 8px;
	text-align: center;
}

.btn-flat:disabled {
	background: var(--colour4);
	border-color: var(--colour3);
	color: var(--colour3);
	cursor: default;
}

.btn-flat:active {
	border-color: var(--colour2);
	color: var(--colour2);
}

.btn-flat:focus {
	outline: none;
}

/*** Toggle Switch styles ***/
.tgl-switch {
	vertical-align: middle;
	display: inline-block;
}

.tgl-switch .tgl {
	display: none;
}

.tgl,
.tgl:after,
.tgl:before,
.tgl *,
.tgl *:after,
.tgl *:before,
.tgl+.tgl-slider {
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}

.tgl::-moz-selection,
.tgl:after::-moz-selection,
.tgl:before::-moz-selection,
.tgl *::-moz-selection,
.tgl *:after::-moz-selection,
.tgl *:before::-moz-selection,
.tgl+.tgl-slider::-moz-selection {
	background: none;
}

.tgl::selection,
.tgl:after::selection,
.tgl:before::selection,
.tgl *::selection,
.tgl *:after::selection,
.tgl *:before::selection,
.tgl+.tgl-slider::selection {
	background: none;
}

.tgl-slider {}

.tgl+.tgl-slider {
	outline: 0;
	display: block;
	width: 40px;
	height: 18px;
	position: relative;
	cursor: pointer;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;

}

.tgl+.tgl-slider:after,
.tgl+.tgl-slider:before {
	position: relative;
	display: block;
	content: "";
	width: 50%;
	height: 100%;
}

.tgl+.tgl-slider:after {
	left: 0;
}

.tgl+.tgl-slider:before {
	display: none;
}

.tgl-flat+.tgl-slider {
	padding: 2px;
	-webkit-transition: all .2s ease;
	transition: all .2s ease;
	background: var(--colour6);
	border: 3px solid var(--colour4);
	border-radius: 2em;
}

.tgl-flat+.tgl-slider:after {
	-webkit-transition: all .2s ease;
	transition: all .2s ease;
	background: var(--colour4);
	content: "";
	border-radius: 1em;
}

.tgl-flat:checked+.tgl-slider {
	border: 3px solid var(--colour5);
}

.tgl-flat:checked+.tgl-slider:after {
	left: 50%;
	background: var(--colour5);
}

/*** Toggle Switch styles ***/

.btn-apply {
	display: block !important;
	margin-left: auto;
	margin-right: auto;
	width: 40%;
}

/*** Form Controls ***/

.form-control {
	background-color: var(--colour6);
	border: 2px solid var(--colour7);
	border-radius: 4px;
	color: var(--colour4);
}

.form-control:hover {
	border-color: var(--colour4);
}

.form-group {
	padding-top: 4px;
	display: grid;
	grid-template-columns: 50% 50%;
	row-gap: 4px;
	padding-right: 10px;
	padding-left: 10px;
}

.form-group label {
	color: var(--colour4);
	vertical-align: middle;
	font-weight: normal;
}

section {
	display: flex;
	flex-direction: column;
}

section> :first-child {
	margin-top: 4px;
	margin-bottom: 4px;
	font-weight: bold;
	justify-content: space-between;
	display: flex;
	flex-direction: row;
	align-items: baseline;
}

.settingsContainer {
	border-bottom: 1px solid var(--colour2);
	padding-top: 10px;
	padding-bottom: 10px;
}

.collapse {
	padding-left: 5%;
}

/* STREAM TOOLS */
#streamTools {
	-moz-border-radius-bottomright: 5px;
	-moz-border-radius-bottomleft: 5px;
	-webkit-border-bottom-right-radius: 5px;
	-webkit-border-bottom-left-radius: 5px;
	border-bottom-right-radius: 5px;
	/* future proofing */
	border-bottom-left-radius: 5px;
	/* future proofing */
	-khtml-border-bottom-right-radius: 5px;
	/* for old Konqueror browsers */
	-khtml-border-bottom-left-radius: 5px;
	/* for old Konqueror browsers */

	-webkit-touch-callout: none;
	/* iOS Safari */
	-webkit-user-select: none;
	/* Safari */
	-khtml-user-select: none;
	/* Konqueror HTML */
	-moz-user-select: none;
	/* Firefox */
	-ms-user-select: none;
	/* Internet Explorer/Edge */
	user-select: none;
	/* Non-prefixed version, currently
                                  supported by Chrome and Opera */

	position: absolute;
	top: 0;
	right: 2%;
	z-index: 100;
	border: 4px solid var(--colour8);
	border-top-width: 0px;
}

#streamToolsHeader {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	border-bottom: 1px solid var(--colour8);
	background-color: var(--colour7);
}

.streamTools {
	background-color: var(--colour2);
	font-family: var(--buttonFont);
	font-weight: lighter;
	color: var(--colour4);
}

.streamTools-shown>#streamToolsSettings,
.streamTools-shown>#streamToolsStats {
	display: block;
}

#streamToolsToggle {
	width: 100%;
}

#qualityStatus {
	font-size: 37px;
	padding-right: 4px;
}

#streamToolsSettings,
#streamToolsStats {
	font-size: 1em;
	/*display: none;*/
}

/* STREAM TOOLS */


#streamingVideo{
	position: absolute;
	height: 100%;
	width:100%;
	/* object-fit: cover; */
}
#conf-panel{
	z-index: 9999;
	position: fixed;
	bottom: 0;
	right: 0;
	/* display: none; */
}




.offcanvas{
	filter: drop-shadow(0 0 0.75rem black);

	/* background: rgba(0,0,0,0.8); */
	backdrop-filter: saturate(180%) blur(10px);
}

#video-background {
    position: fixed;s
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    object-fit: cover;
	
  }

#messageOverlayInner{
    position: absolute;
    bottom: 50px;
    /* left: 50%; */
    /* transform: translate(-50%, 0); */
    background: #222;
    padding: 2rem;
    border-radius: 50px;
}
