// Import Bootstrap CSS first
import 'bootstrap/dist/css/bootstrap.min.css';
import './assets/css/index.css';

// Then import Bootstrap JS
import * as bootstrap from 'bootstrap';

// Import Agora for conferencing
import { Agora } from './agora';

// Declare StreamPixel SDK module for TypeScript
declare module 'streampixelsdk' {
  export function StreamPixelApplication(config: any): Promise<any>;
}

// Import StreamPixel SDK
import { StreamPixelApplication } from 'streampixelsdk';

// Import StreamPixel configuration
import { streamPixelConfig } from './streamPixelConfig.js';

// Helper function to emit UI events to StreamPixel
function emitStreamPixelEvent(eventData: any) {
  if (window.streamPixelApp && window.streamPixelApp.appStream && window.streamPixelApp.appStream.stream) {
    window.streamPixelApp.appStream.stream.emitUIInteraction(eventData);
  } else {
    console.warn('StreamPixel not initialized, cannot emit event:', eventData);
  }
}

// Define global window interface
declare global {
  interface Window {
    streamPixelApp: any | null;
    init: () => void;
    initStreamPixel: () => Promise<void>;
    cw: any;
  }
}

// Global variables
let isPlaying = false;

// Sample Dolby token (not used in current implementation)
const dolbyToken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkb2xieS5pbyIsImlhdCI6MTY4Mzc0NDA1MCwic3ViIjoiall6dEtmZXhXWWRBYTdIRUYweHVMdz09IiwiYXV0aG9yaXRpZXMiOlsiUk9MRV9DVVNUT01FUiJdLCJ0YXJnZXQiOiJzZXNzaW9uIiwib2lkIjoiZWRmM2FjNzctYzlmOS00NzFmLThhOTEtZjUxM2ZhNjY0MDNjIiwiYWlkIjoiMjUwYmE3YzgtNzc3Yi00NDVhLTkyMTgtZDBjNTQ1OWUyMmYzIiwiYmlkIjoiOGEzNjhlNjI4NjgzMmI3ZTAxODZhNzgxMDBlNDdjMmQiLCJleHAiOjE2ODM3NDc2NTB9.jkPRjyTHUy7WhTTkJUYCOPqQ1cZ9ESHDTrkwLZC5zk8XKyYMlAuhV6d4iSDMRwlcrOv4TyqZWTkSvuF1x-ZMGw";

// Initialize Agora for conferencing
let agora = new Agora();

// Avatar information
let avatarURL = "https://api.readyplayer.me/v1/avatars/636d90b73c0e9a4191dc9660.glb";
let newAvatarURL: string;
const subdomain = 'theomniversecity'; // ReadyPlayerMe subdomain

window.addEventListener('message', subscribe);
document.addEventListener('message', subscribe);

// Listen for parent iframe messages (including "init" command)
window.addEventListener('message', function(event) {
  // Handle parent iframe commands
  if (event.data && typeof event.data === 'object') {
    if (event.data.command === 'init') {
      console.log('Received init command from parent iframe');
      init();
    }
  }
});

// Avatar click handler will be set up in init() function


async function fetchRpmToken(): Promise<string | null> {
  // Check if baseURL is defined
  if (!baseURL) {
    console.error('Base URL is not defined. User may not be logged in.');
    alert('Please log in to access this feature.');
    return null;
  }

  const rpmTokenUrl = `${baseURL}/wp-json/oc-api/v1/get-rpm-token`;
  console.log('Fetching RPM token from:', rpmTokenUrl);

  try {
    const response = await makeAuthorizedRequest(rpmTokenUrl, {
      method: 'GET',
    });

    if (response.ok) {
      const data = await response.json();
      return data.short_lived_token;
    } else {
      console.error('Failed to fetch RPM token');
      return null;
    }
  } catch (error) {
    console.error('Error fetching RPM token:', error);
    return null;
  }
}




/**
 * Opens the ReadyPlayerMe avatar selection interface
 * Handles authentication and token retrieval
 */
async function selectAvatar() {
  // Check if baseURL is defined - this is needed for authentication
  if (!baseURL) {
    console.error('Base URL is not defined. User may not be logged in.');
    alert('Please log in to access this feature.');
    return;
  }

  // Initial URL without token
  let src = `https://${subdomain}.readyplayer.me/avatar?clearCache&frameApi`;

  // Open the iframe with initial URL
  openIFrame({url: src});

  // Get the frame element
  const frameElement = document.getElementById('frame') as HTMLIFrameElement;
  if (!frameElement) {
    console.error('Frame element not found');
    return;
  }

  frameElement.hidden = true;

  // Fetch the RPM token from the backend
  console.log('Fetching RPM token...');
  const rpmToken = await fetchRpmToken();

  // If token was successfully retrieved, update the iframe source
  if (rpmToken) {
    console.log('RPM token received, updating iframe source');
    src = `https://${subdomain}.readyplayer.me/avatar?clearCache&frameApi&token=${rpmToken}`;
    frameElement.hidden = false;
    frameElement.src = src;
  } else {
    console.error('Failed to get RPM token');
  }
}





// prep the player div element
let playerElement = document.getElementById("player") as HTMLDivElement;

// Create a config object


const params = new URLSearchParams(window.location.search)
console.log(params);

/**
 * Encodes a string to Base64 using modern APIs
 * @param str - The string to encode
 * @returns The Base64 encoded string
 */
function encodeToBase64(str: string): string {
  // Use TextEncoder to handle UTF-8 encoding
  const encoder = new TextEncoder();
  const data = encoder.encode(str);

  // Convert to base64 using modern APIs
  let binary = '';
  const bytes = new Uint8Array(data);
  const len = bytes.byteLength;

  for (let i = 0; i < len; i++) {
    binary += String.fromCharCode(bytes[i]);
  }

  return btoa(binary);
}

/**
 * Decodes a Base64 string using modern APIs
 * @param base64 - The Base64 string to decode
 * @returns The decoded string
 */
function decodeFromBase64(base64: string): string {
  try {
    // Handle potential errors with invalid base64 input
    const binary = atob(base64);
    const bytes = new Uint8Array(binary.length);

    for (let i = 0; i < binary.length; i++) {
      bytes[i] = binary.charCodeAt(i);
    }

    // Use TextDecoder to handle UTF-8 decoding
    return new TextDecoder().decode(bytes);
  } catch (error) {
    console.error('Error decoding Base64 string:', error);
    return '{}'; // Return empty object string as fallback
  }
}


/**
 * User and session information
 */
interface UserInfo {
  username: string;
  email: string;
  token: string;
  refresh_token: {
    token: string;
    expires: number;
  };
  baseURL: string;
  avatar?: string;
}

// Parse URL parameters
const urlParams = new URLSearchParams(window.location.search);

// User information with defaults
let username = urlParams.get('username') || 'test';
let email = urlParams.get('email') || '<EMAIL>';
let name = urlParams.get('name') || 'testName';
let jwt = '';
let refresh_token = '';
let refresh_token_expires = 0;
let baseURL = '';
let token = '';
let userInfo: Partial<UserInfo> = {};

// Session information
let sessionId = "";
let lastSessionId = "";
let currentMap = "";
let isHost = false;
let isInSession = false;

// Parse position from URL parameters
let posJSON = decodeFromBase64(urlParams.get('pos') || "eyJ4IjowLCJ5IjowLCJ6IjowfQ==");
let position = JSON.parse(posJSON);

// Interval for OCX deduction
let ocxInterval: ReturnType<typeof setInterval>;


let joinBtn = document.getElementById('join-conf');
let leaveBtn = document.getElementById('leave-conf');


type Transform = {
  x:string,
  y:string,
  z:string,
  roll:string,
  pitch:string,
  yaw:string
}
type POI = {
     Name:string;
     Transform:Transform;
}


let poiArray:POI[] = [];


joinBtn.addEventListener('click', async ()=>{
  // await dolby.leaveConference();
  // await dolby.createAndJoinConference(confAlias, username, avatarURL, position);

})

/**
 * Opens an iframe in a modal dialog
 * @param args - Object containing the URL to open
 */
function openIFrame(args: { url: string }): void {
  // First, clean up any existing modal backdrops
  document.querySelectorAll('.modal-backdrop').forEach(e => e.remove());

  // Get the iframe wrapper and clear its contents
  const iframeWrapper = document.querySelector("#iframe-wrapper") as HTMLElement;
  if (!iframeWrapper) {
    console.error('Iframe wrapper not found');
    return;
  }

  iframeWrapper.innerHTML = "";

  // Create a new iframe element
  const iFrameOverlay = document.createElement('iframe');
  iFrameOverlay.src = args.url;
  iFrameOverlay.width = '100%';
  iFrameOverlay.height = '100%';
  iFrameOverlay.id = 'frame';
  iFrameOverlay.setAttribute('allow', 'microphone *;camera *;clipboard-write *');
  iFrameOverlay.style.backgroundColor = "transparent";

  // Add the iframe to the wrapper
  iframeWrapper.appendChild(iFrameOverlay);

  // Get the modal element
  const modalElement = document.getElementById('exampleModal');
  if (!modalElement) {
    console.error('Modal element not found');
    return;
  }

  // Remove any existing event listeners to prevent duplicates
  const newModalElement = modalElement.cloneNode(true);
  if (modalElement.parentNode) {
    modalElement.parentNode.replaceChild(newModalElement, modalElement);
  }

  const newModal = newModalElement as HTMLElement;

  // Get or create the modal instance
  const myModal = bootstrap.Modal.getInstance(newModal) || new bootstrap.Modal(newModal, {
    keyboard: true,
    focus: true
  });

  // Add event listener for when the modal is hidden
  newModal.addEventListener("hidden.bs.modal", () => {
    // Clean up
    iframeWrapper.innerHTML = "";
    document.querySelectorAll(".modal-backdrop").forEach(e => e.remove());

    // Resume the player
    emitStreamPixelEvent({
      type: "bridge",
      event: JSON.stringify({
        "action": "resume"
      })
    });
  });

  // Pause the player
  emitStreamPixelEvent({
    type: "bridge",
    event: JSON.stringify({
      "action": "pause"
    })
  });

  // Show the modal
  myModal.show();
}

function redirect(args:any){

  if(args.target){
    // window.top.open(args.url);
    let data = {
      url:args.url,
      command:'openLink'
    }
    window.parent.postMessage(data, '*')
  }else{
    window.top.location.href = args.url;
  }

}

function goToPOI(index:number){


  let offcanvasElement = document.getElementById('poiPanel') as HTMLElement;

  // Get the Bootstrap Offcanvas instance and hide it
  let offcanvasInstance = bootstrap.Offcanvas.getInstance(offcanvasElement);
  if (offcanvasInstance) {
    offcanvasInstance.hide(); // Hide the offcanvas
  }


  let poi:POI = poiArray[index]

  emitStreamPixelEvent({
    type:"bridge",
    event:JSON.stringify({
      "action":"transformTo",
      "args":{
        "x":parseInt(poi.Transform.x),
        "y":parseInt(poi.Transform.y),
        "z":parseInt(poi.Transform.z),
        "roll":parseInt(poi.Transform.roll),
        "pitch":parseInt(poi.Transform.pitch),
        "yaw":parseInt(poi.Transform.yaw),
      }
    })
  });
}

function updateURL(){
  if(currentMap != ''){
    urlParams.set('map', currentMap)

  }
  if(sessionId != ''){
    urlParams.set('sessionId', sessionId)
  }

  // urlParams.append('position', position)
  let data = {
    url:urlParams.toString(),
    command:'changeURL'
  }
  window.parent.postMessage(data, '*')
}

// Flag to prevent duplicate initialization
let initialCommandsSent = false;

// Function to handle initial level loading and session setup after StreamPixel is ready
function onStreamPixelReady() {
  if (initialCommandsSent) {
    console.log('Initial commands already sent, skipping...');
    return;
  }

  initialCommandsSent = true;
  console.log('StreamPixel is ready, sending initial commands...');

  // Send volume setting
  emitStreamPixelEvent({
    type:"bridge",
    event:JSON.stringify({
      "action":"setVolume",
      "args":{
        "volume":Number((musicVolume as HTMLInputElement).value)/100
      }
    })
  });

  // Handle session joining or level loading
  if(params.get('sessionId')){
    console.log("Joining session:", params.get('sessionId'));
    emitStreamPixelEvent({
      type:"bridge",
      event:JSON.stringify({
        "action":"joinSession",
        "args":{
          "sessionId": params.get("sessionId")
        }
      })
    });
  } else {
    const mapName = params.get("map") || "Omniverse_Map";
    console.log("AUTO: Loading map:", mapName);
    console.log("AUTO: StreamPixel app state:", !!window.streamPixelApp);
    console.log("AUTO: Position:", position);

    const teleportEvent = {
      type:"bridge",
      event:JSON.stringify({
        "action":"teleportToMap",
        "args":{
          "map": mapName,
          "position": position
        }
      })
    };

    console.log("AUTO: Sending teleport event:", teleportEvent);
    emitStreamPixelEvent(teleportEvent);
  }
}

let onResponse = async function(e:any){


    let response = JSON.parse(e);
    console.log(e)
    if('type' in response && response.type == "audioEvent" && response.body == "complete"){
      speechController.onUtteranceComplete()
    }

    if('event' in response && response.event == "bridge"){

      // console.log(response.data.name);
      switch(response.data.name){
        case "locationUpdated":
          // history.pushState(
          //   {},
          //   null,
          //   route.path + '/store/abcd/coords/50:50:50')

          // console.log(response.data.args);;
          // position = {
          //   x: response.data.args.x || 0,
          //   y: response.data.args.y || 0,
          //   z: response.data.args.z || 0,
          // }
          // let direction = {
          //   x: response.data.args.yaw || 0,
          //   y: response.data.args.pitch || 0,
          //   z: response.data.args.roll || 0,
          // }
          // dolby.setSpatialPosition(position);
          // dolby.setSpatialDirection(direction);

          if (currentMap != response.data.args.map){
            currentMap =  response.data.args.map;
            updateURL();
          };


          break;
        case "openIFrame":
          openIFrame(response.data.args);
          break;
        case "POI":

        setTimeout(()=>{

          let buttonElement = document.getElementById('poiPanelBtn') as HTMLElement;

          // Check if the button element exists
          if (buttonElement) {
            buttonElement.click(); // Programmatically trigger the click event
            console.log("Button clicked");
          } else {
            console.error("Button element not found");
          }

        }, 5000)
          console.log(JSON.stringify(response.data.args));
          document.getElementById("poiPanelBtn").hidden = false;


           poiArray = response.data.args.POI;
            var poiList = document.getElementById("poi-list");
            poiList.innerHTML = "";
            poiArray.forEach((element:POI, index:number)=>{
                let poiName = element.Name;
                let listItem = document.createElement("li");
                listItem.innerHTML = `
                <a class="poiButton" href="#">${poiName}</a>

                `;

                listItem.addEventListener("click", function() {
                  goToPOI(index);
              });


                poiList.appendChild(listItem);

            })




          break;

        case "redirect":
          redirect(response.data.args);
          break;
        case "join-conf":
          console.log("join-conf")
          await agora.leave()
          await agora.join(response.data.args.alias, username);

          break;
        case "leave-conf":
          console.log("leave-conf")
          await agora.leave();
          break;
        case "gtag":
            //dolby.leaveConference();
            break;
        case "session-started":
              sessionId = response.data.args.sessionId;
              lastSessionId = sessionId;
              isHost = response.data.args.isHost;
              isInSession = true;
              document.getElementById('host-conf').hidden = true;
              //dolby.leaveConference();
              updateURL();
              break;
        case "session-ended":
          sessionId = "";
          isHost = false;
          isInSession = false;
          document.getElementById('host-conf').hidden = false;
          //dolby.leaveConference();
          break;
        case "levelChange":
          //dolby.leaveConference();
          break;
        case "open_bot_chat":
          console.log("open-bot-chat")
          document.getElementById('chat').hidden = false;
          let cw = (window as any).cw
          console.log("updating url to ", response.data.args.url)
          cw.init(document.getElementById('chat'), {url: "https://2rosytd52oq66z5f2wtq4zhds40qexrr.lambda-url.us-east-1.on.aws/", locale:"en", assistantId:"asst_IeurMx1WCMWeoP41PXM8Z0oQ", speechController:speechController, config:{openOnInit:true, showToggleButton:false} });
          // cw.init(document.getElementById('chat'), {pixelstream:RTCPlayer, url: response.data.args.url});

          // cw.init(document.getElementById('chat'), {pixelstream:RTCPlayer, url: response.data.args});
          // console.trace(cw)
          break;
        case "close_bot_chat":
          console.log("close-bot-chat")
            document.getElementById('chat').hidden = true;
            // cw.init(document.getElementById('chat'), {pixelstream:RTCPlayer, url: "https://oc-gpt-server.onrender.com"});
            // console.trace(cw)
            break;
        case "ocx_over":
          redirect({url:"https://theomniverse.city/"});
            // cw.init(document.getElementById('chat'), {pixelstream:RTCPlayer, url: "https://oc-gpt-server.onrender.com"});
            // console.trace(cw)
            break;
        case "init":
          console.log("Unreal Engine sent init event - now ready to receive commands!");
          window.parent.postMessage({ command: 'getUserInfo' }, '*')
          console.log("init: get user info")

          // Now that Unreal is ready, send initial level loading commands
          onStreamPixelReady();

          break;
  }
    }
    /*
{"type":"bridge","event":{"args":{"map":"STORE_1","x":"385.408498","y":"83.463891","z":"99.563929"},"name":"locationUpdated"}}
response {"type":"bridge","event":{"args":{"map":"STORE_1","x":"385.408498","y":"210.413403","z":"99.563929"},"name":"locationUpdated"}}
    */

    if ('Method' in response){
        if(response.Method == "LaunchURL"){
            // window.top.open(response.Argument);
            let data = {
              url:response.Argument,
              command:'openLink'
            }
            window.parent.postMessage(data, '*')
        }else if(response.Method == "GetAvatar"){
            let messageObject = {
                PlayerConfig:{
                    "RPM":avatarURL,
                    "name":name
                }
            }


            emitStreamPixelEvent(messageObject);
        } else if(response.Method == "OpenIframe"){
            let argumentsObject = JSON.parse(response.Argument);
            if(argumentsObject.url){
                const frameElement = document.getElementById('frame') as HTMLIFrameElement;
                if (frameElement) {
                    frameElement.src = argumentsObject.url;
                    frameElement.hidden = false;
                }
            }

        }
    }

}


let onVideoInit = function(){

  console.log('video init')
  // let videoLoader = document.getElementById("video-background") as HTMLVideoElement;
  // videoLoader.pause();
  // videoLoader.hidden = true;
  document.getElementById('am-container').hidden = false;


  document.getElementById("omniverse-controls").hidden = false;
  document.getElementById("am-container").style.background = "black";

  type DataType = {
    action: string;
    args: {
      avatarURL: string;
      displayName?: string;
      baseURL?: string;
      token?: string;
      onMobile?: boolean;
      sessionId?: string;
    }
  }
  let data:DataType = {
    "action":"config",
    "args":{
      "avatarURL":avatarURL,
      onMobile:window.matchMedia( "(hover: none)" ).matches
    }
  }
  if(params.get('sessionId')){
    data.args['sessionId'] = params.get('sessionId');
  }
  console.log("emit event", data)


  // deductOCX(baseURL);


  emitStreamPixelEvent({
    type:"bridge",
    event:JSON.stringify({
      "action":"setVolume",
      "args":{
        "volume":Number((musicVolume as HTMLInputElement).value)/100
      }
    })
  });

  // Note: Level loading and session joining moved to onStreamPixelReady()
  // to ensure StreamPixel is fully connected before sending events



}

document.ontouchmove = (event: TouchEvent) => {
    event.preventDefault();
}



function subscribe(event:MessageEvent) {
  const json = parse(event);
  console.log("----here message data ---", JSON.stringify(event))
  console.log()
  if (json?.source !== 'readyplayerme') {
    return;
  }



  // Susbribe to all events sent from Ready Player Me once frame is ready
  if (json.eventName === 'v1.frame.ready') {
    let f = document.getElementById("frame") as HTMLIFrameElement
    f.contentWindow.postMessage(
      JSON.stringify({
        target: 'readyplayerme',
        type: 'subscribe',
        eventName: 'v1.**'
      }),
      '*'
    );


  }
  if (json.eventName === 'v1.user.authorized') {
    console.log(`USER ID: ${json.data.id}`);

    const data = new FormData();
    data.append('rpm_user_id', json.data.id);


    // send a post request to the server to link the ready player me user id to the wordpress user id
    makeAuthorizedRequest(`${baseURL}/wp-json/oc-api/v1/link-rpm-account`, {
        method: 'POST',
        headers: {},
        body: data
    }).then(()=>{
      console.log("RPM linked")
    })
  }



  // Get avatar GLB URL
  if (json.eventName === 'v1.avatar.exported') {
    console.log(`Avatar URL: ${json.data.url}`);
    newAvatarURL = avatarURL = json.data.url;

    (document.getElementById('avatar') as HTMLImageElement).src = (avatarURL).replace("glb", "png") + '?size=256';
    // document.getElementById('avatarUrl').innerHTML = `Avatar URL: ${json.data.url}`;

   console.log("set avatar image to " + (document.getElementById('avatar') as HTMLImageElement).src)

   let frame = document.getElementById('frame') as HTMLIFrameElement;

    // Clean up any existing modal backdrops
    document.querySelectorAll('.modal-backdrop').forEach(e => e.remove());

    // Get the modal element and hide it
    const modal = document.getElementById('exampleModal');
    const myModal = bootstrap.Modal.getInstance(modal);
    if (myModal) {
      myModal.hide();
    }

    // Clear iframe content by setting its src to `about:blank`
    if(frame){
      frame.src = 'about:blank';
      frame.hidden = true; // Optional, hide the iframe as well if needed
    }

    // Show main content or perform any additional setup after avatar selection



    document.getElementById('am-container').hidden = false;

    const data = new FormData();
    data.append('avatar_url', json.data.url);
    makeAuthorizedRequest(`${baseURL}/wp-json/oc-api/v1/avatar`, {
        method: 'POST',
        headers: {},
        body: data
    }).then(()=>{

      type DataType = {
        action: string;
        args: {
          avatarURL: string;
        }
      }


      let data:DataType = {
        "action":"config",
        "args":{
          "avatarURL":avatarURL,
        }
      }
      if(window.streamPixelApp){
        emitStreamPixelEvent({
        type:"bridge",
        event:JSON.stringify(data)
      });
      }

      // window.parent.postMessage({ command: 'getUserInfo' }, '*')
    })
    document.getElementById('am-container').focus();
    // RTCPlayer.delegate.onConnectAction();
    // RTCPlayer.delegate.onWebRtcAutoConnect();
    // RTCPlayer.connectToSignallingSever();

    // let videoLoader = document.getElementById("video-background") as HTMLVideoElement;
    // videoLoader.play();
    // videoLoader.volume = 0.2;
    // videoLoader.hidden = false;


    // window.ArcanePlayer.play();

    const event = new MouseEvent('click', {
      bubbles: true, // Whether the event bubbles up through the DOM or not
      cancelable: true, // Whether the event is cancelable
      view: window // The window that contains the event
  });

  // Dispatch the click event on the selected element


    document.getElementById("control-bar").dispatchEvent(event)



    // window.focus();
    // document.getElementById("playerUI").focus();

  }

  // Get user id
  if (json.eventName === 'v1.user.set') {
    console.log(`User with id ${json.data.id} set: ${JSON.stringify(json)}`);
  }
}

function parse(event:MessageEvent) {
  try {
    return JSON.parse(event.data);
  } catch (error) {
    return null;
  }
}

// Function removed as it was unused

// RTCPlayer.delegate.onWebRtcAnswer= ()=>{
//   RTCPlayer.delegate.showTextOverlay("Welcome to the Omniverse City");
// }

// /**
//  * Shows a text overlay to alert the user the stream is currently loading
//  */
// RTCPlayer.delegate.onStreamLoading = ()=>{
//   RTCPlayer.delegate.showTextOverlay("Loading Stream");
// }

let createSessionBtn = document.getElementById("create-session-btn")
let joinSessionBtn = document.getElementById("join-session-btn")
let rejoinSessionBtn = document.getElementById("rejoin-session-btn")

let musicVolume = document.getElementById("musicVolume")
let confVolume = document.getElementById("confVolume")

let muteMicToggle = document.getElementById("muteMicToggle")
let toggleMiniMap = document.getElementById("toggleMiniMap")

muteMicToggle.addEventListener('change', async function (this:HTMLInputElement) {
  if (this.checked) {
    // Switch is turned on
    await agora.setMicInput(true)
  } else {
    await agora.setMicInput(false)
    // Switch is turned off
    // Handle the off state or call some other function
  }
});

toggleMiniMap.addEventListener('change', function (this:HTMLInputElement) {
  if (this.checked) {
    // Switch is turned on
    if(window.streamPixelApp){

      emitStreamPixelEvent({
          type:"bridge",
          event:JSON.stringify(
            {
              "action":"config",
              "args":{
                "showMinimap":true
              }
            })
      });
    }
  } else {
    if(window.streamPixelApp){

      emitStreamPixelEvent({
          type:"bridge",
          event:JSON.stringify(
            {
              "action":"config",
              "args":{
                "showMinimap":false
              }
            })
      });
    }
    // Switch is turned off
    // Handle the off state or call some other function
  }
});

musicVolume.addEventListener('input', function (this: HTMLInputElement) {
  // Display the current slider value
  // Note: StreamPixel doesn't have built-in volume control like ArcanePlayer
  // We send a custom message to Unreal Engine to handle volume changes
  if(window.streamPixelApp){
    emitStreamPixelEvent({
      type:"bridge",
      event:JSON.stringify({
         "action":"setVolume",
        "args":{
          "volume":Number(this.value)/100
        }
      })
    });
  }
});
confVolume.addEventListener('input', function (this: HTMLInputElement) {
  // Display the current slider value

  agora.setVolume(Number(this.value))

});



joinSessionBtn.addEventListener('click', async ()=>{
  emitStreamPixelEvent({
    type:"bridge",
    event:JSON.stringify({
      "action":"joinSession",
      "args":{
        "sessionId": (document.getElementById("session-id-input") as HTMLInputElement).value
      }
    })
  });

})
createSessionBtn.addEventListener('click', async ()=>{
  emitStreamPixelEvent({
    type:"bridge",
    event:JSON.stringify({
      "action":"hostSession",
      "args":{
      }
    })
  });

})
rejoinSessionBtn.addEventListener('click', async ()=>{
  emitStreamPixelEvent({
    type:"bridge",
    event:JSON.stringify({
      "action":"joinSession",
      "args":{
        "sessionId": lastSessionId
      }
    })
  });

})

/**
 * Speech controller for text-to-speech functionality
 */
const speechController = {
  /**
   * Stop any currently playing speech
   */
  stop(): void {
    const messageObject = {
      type: "stopAudio",
      args: "",
    };
    emitStreamPixelEvent(messageObject);
  },

  /**
   * Speak the provided text
   * @param utteranceText - The text to speak
   * @param voiceName - Optional voice name (not currently used)
   */
  speak(utteranceText: string, voiceName: string = 'default'): void {
    console.log("Speaking text:", utteranceText);
    const messageObject = {
      type: "say",
      args: "<speak>" + utteranceText + "</speak>",
    };
    emitStreamPixelEvent(messageObject);
  },

  /**
   * Pause speech (not implemented)
   */
  pause(): void {
    // Not implemented
  },

  /**
   * Resume speech (not implemented)
   */
  resume(): void {
    // Not implemented
  },

  /**
   * Called when utterance is complete (not implemented)
   */
  onUtteranceComplete(): void {
    // Not implemented
  }
}

export function init(){

// Check Bootstrap initialization
console.log('Bootstrap loaded:', !!bootstrap);
console.log('Modal class available:', typeof bootstrap.Modal !== 'undefined');
console.log('Offcanvas class available:', typeof bootstrap.Offcanvas !== 'undefined');

// Initialize Bootstrap components
document.querySelectorAll('[data-bs-toggle="tooltip"]').forEach(el => {
  new bootstrap.Tooltip(el);
});

// Clean up any lingering modal backdrops
document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
  backdrop.remove();
});

// Ensure all modals are properly hidden on init
document.querySelectorAll('.modal').forEach(modalEl => {
  const modalInstance = bootstrap.Modal.getInstance(modalEl);
  if (modalInstance) {
    modalInstance.hide();
  }
});

  window.addEventListener('focus', function() {
    console.log("iframe window focus");
  });


  // Set up avatar click handler
  const avatarElement = document.getElementById('avatar');
  if (avatarElement) {
    avatarElement.addEventListener('click', selectAvatar);
  }

  // Set up StreamPixel container click handler
  const streamPixelContainer = document.getElementById('streampixel-container');
  if (streamPixelContainer) {
    streamPixelContainer.addEventListener("click", function(){
      console.log("StreamPixel player clicked")
      window.focus();
      streamPixelContainer.focus();
    });
  }
  document.addEventListener('focusout', () => {
    // Use a setTimeout to ensure the focus event has truly moved outside
    setTimeout(() => {
        if (!document.activeElement || document.activeElement === document.body) {
          console.log("focus lost"); // Focus is outside the iframe
        }
    }, 0);
  });
  document.addEventListener('focusin', () => {
    console.log("focus gained"); // Focus is within the iframe
  });



  let agoraJoin = document.getElementById("agora-join");
  agoraJoin.addEventListener('click', function(){
    agora.join("test", Math.floor(Math.random()*100))
  })
  let agoraLeave = document.getElementById("agora-leave");
  agoraLeave.addEventListener('click', function(){
    agora.leave();
  })

  console.log("init")

  // Initialize StreamPixel
  initStreamPixel();

  // Keep am-container visible from the start
  document.getElementById('am-container').hidden = false;

  // document.getElementById('frame').hidden = false;
  // document.getElementById('video-background').hidden = true;
  let cw = (window as any).cw;
  cw.init(document.getElementById('chat'), {url: "https://2rosytd52oq66z5f2wtq4zhds40qexrr.lambda-url.us-east-1.on.aws/", locale:"en", assistantId:"asst_IeurMx1WCMWeoP41PXM8Z0oQ", speechController:speechController, config:{openOnInit:true, showToggleButton:false} });


// StreamPixel initialization function
async function initStreamPixel() {
  try {
    console.log('Initializing StreamPixel...');
    console.log('StreamPixel config:', streamPixelConfig);

    // Add timeout to prevent hanging
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('StreamPixel initialization timeout after 30 seconds')), 30000);
    });

    const streamPixelApp = await Promise.race([
      StreamPixelApplication(streamPixelConfig),
      timeoutPromise
    ]) as any;

    console.log('StreamPixel application created:', streamPixelApp);
    window.streamPixelApp = streamPixelApp;

    // Get the container and append the stream
    const container = document.getElementById('streampixel-container');
    if (container && streamPixelApp.appStream && streamPixelApp.appStream.rootElement) {
      console.log('Appending StreamPixel to container');
      container.appendChild(streamPixelApp.appStream.rootElement);
    } else {
      console.warn('Container or rootElement not found:', {
        container: !!container,
        appStream: !!streamPixelApp.appStream,
        rootElement: streamPixelApp.appStream ? !!streamPixelApp.appStream.rootElement : false
      });
    }

    console.log('StreamPixel initialized successfully');

    // Set up event listeners
    setupStreamPixelEventListeners();

    // Don't send commands automatically - wait for Unreal to send "init" event
    console.log('StreamPixel initialized, waiting for Unreal Engine init event...');

  } catch (error) {
    console.error('Failed to initialize StreamPixel:', error);

    // Show error message to user
    const container = document.getElementById('streampixel-container');
    if (container) {
      container.innerHTML = `
        <div style="display: flex; align-items: center; justify-content: center; height: 100%; background: #000; color: #fff; font-family: Arial, sans-serif;">
          <div style="text-align: center;">
            <h2>StreamPixel Connection Failed</h2>
            <p>Error: ${error.message}</p>
            <p>Please check your App ID: ${streamPixelConfig.appId}</p>
            <button onclick="location.reload()" style="padding: 10px 20px; margin-top: 10px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">Retry</button>
          </div>
        </div>
      `;
    }
  }
}

function setupStreamPixelEventListeners() {
  if (!window.streamPixelApp) return;

  const { pixelStreaming } = window.streamPixelApp;
  console.log("StreamPixel loaded", window.streamPixelApp)

  // Set up main response event listener - StreamPixel uses a single general listener
  pixelStreaming.addResponseEventListener('handle_responses', (response: any) => {
      console.log('Received from Unreal:', response);

      // Handle different types of responses based on the message structure
      if (response && typeof response === 'object') {
        // Handle bridge events (our main communication channel)
        if (response.type === 'bridge' || response.event) {
          onResponse(response);
        }

        // Handle stream state events
        if (response.type === 'ready') {
          console.log('StreamPixel ready - stream is connected');
          isPlaying = true;
          setTimeout(onVideoInit, 1000);
        } else if (response.type === 'loading') {
          console.log('StreamPixel loading');
        } else if (response.type === 'afkWarning') {
          console.log('StreamPixel afkWarning');
        } else if (response.type === 'afkWarningDeactivate') {
          console.log('StreamPixel afkWarningDeactivate');
          isPlaying = false;
          clearInterval(ocxInterval);
        } else if (response.type === 'afkTimedOut') {
          console.log('StreamPixel afkTimedOut');
          isPlaying = false;
          clearInterval(ocxInterval);
        }

        // Handle file events (if supported by your Unreal app)
        if (response.type === 'fileProgress') {
          console.log('File download progress:', response.progress);
        } else if (response.type === 'fileReceived') {
          console.log('File received:', response);
          // Note: StreamPixel file handling may be different from ArcanePlayer
          // You'll need to implement this based on your Unreal app's file sending format
        }
      }
  });
}

// Duplicate function definition removed - using the one defined earlier

// Assign to window for global access
window.initStreamPixel = initStreamPixel;


//   fetch('https://charlenes24.sg-host.com/getclientauth.php?cb=3')
//       .then(response => {
//           if (!response.ok) {
//               throw new Error('Network response was not ok');
//           }
//           return response.json();
//       })
//       .then(data => {
//           // 'data' now contains the JSON object returned from the server
//           console.log(data);

//           // Extract the access_token from the JSON object
//           const accessToken = data.access_token;
//           console.log("accessToken",accessToken)
//           dolby.initializeToken(accessToken).then(()=>{
//             // dolby.openSession(username, avatarURL).then(()=> dolby.handleConferenceFlow())
//           });
//       })
//       .catch(error => {
//           console.error('There has been a problem with your fetch operation:', error);
//       });


}


async function refreshAuthToken(): Promise<string | null> {
  // Check if baseURL is defined
  if (!baseURL) {
    console.error('Base URL is not defined. Cannot refresh token.');
    alert('Please log in to access this feature.');
    return null;
  }

  // Check if refresh_token is defined
  if (!refresh_token) {
    console.error('Refresh token is not defined. User may not be logged in.');
    return null;
  }

  const refreshUrl = `${baseURL}/wp-json/oc-api/v1/token/refresh`;
  console.log("Refreshing token using URL:", refreshUrl);

  const options = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      refresh_token: refresh_token,
    }),
  }

  try {
    const response = await fetch(refreshUrl, options);

    if (response.ok) {
      const data = await response.json();
      jwt = data.data.token;
      refresh_token = data.data.refresh_token;
      if(data.refresh_token_expires){
        refresh_token_expires = data.refresh_token_expires;
      }

      return jwt; // Return the new token
    } else {
      console.error('Failed to refresh token');
      // Redirect to login if refresh token fails
      window.top.location.href = `${baseURL}/login`;
      return null;
    }
  } catch (error) {
    console.error('Error refreshing token:', error);
    return null;
  }
}



function isTokenExpired(): boolean {
  // If jwt is not defined, consider it expired
  if (!jwt) {
    console.log('JWT is not defined, considering token expired');
    return true;
  }

  try {
    const currentTime = Math.floor(Date.now() / 1000);

    // Use a safer approach to decode JWT
    const base64Url = jwt.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      window.atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );

    const tokenPayload = JSON.parse(jsonPayload);

    // Add more buffer time (e.g., 30 seconds) to prevent edge cases
    return tokenPayload.exp < (currentTime + 30);
  } catch (error) {
    console.error('Error checking token expiration:', error);
    // If we can't check the token, assume it's expired to be safe
    return true;
  }
}

async function makeAuthorizedRequest(url: string, options: RequestInit): Promise<Response> {
  let retryCount = 0;
  const maxRetries = 3;

  while (retryCount < maxRetries) {
    try {
      if (isTokenExpired()) {
        console.log('Token expired, refreshing...');
        const newToken = await refreshAuthToken();

        if (!newToken) {
          throw new Error('Token refresh failed');
        }
      }

      options.headers = {
        ...options.headers,
        Authorization: `Bearer ${jwt}`,
      };

      const response = await fetch(url, options);

      if (response.status === 401) {
        retryCount++;
        // Force token refresh on next iteration
        jwt = '';
        continue;
      }

      return response;
    } catch (error) {
      retryCount++;
      if (retryCount === maxRetries) {
        throw error;
      }
    }
  }

  throw new Error('Max retries exceeded');
}

/**
 * Deducts OCX tokens from the user's wallet
 * This function is currently not used but kept for future implementation
 * @param baseURL - The base URL for the API
 */
function deductOCX(baseURL: string): void {
  console.log("Deducting OCX tokens");
  console.log("Base URL:", baseURL);
  const url = `${baseURL}/wp-json/ocx-wallet/v1/deduct`;

  const data = new FormData();
  data.append('amount', '0.065');

  const headers = {

  };



  // Function to check balance and handle response
  const handleResponse = async (response: Response) => {

    if (response.status === 401) {
      // Token may have expired
      console.error('Unauthorized: Access token may have expired.');
      console.log(response)
      // Redirect or handle token refresh logic here
      window.top.location.href = `${baseURL}/token-expired/`;  // Example page for token expiry


      return;
  }


      try {
          const result = await response.json();
          if (response.status === 200 && result.balance === 0) {

              window.top.location.href = `${baseURL}/omnicast-times-up/`;
          } else {
              console.log('Transaction Successful:', result);
              document.getElementById("ocx").innerText = `${(result.balance * 100).toFixed(2)} OCX` ;
          }
      } catch (error) {
          console.error('Error:', error);
          window.top.location.href = `${baseURL}/omnicast-times-up/`;
      }
  };
console.log("aaa---")
  // Function to execute every minute
  const makeRequest = () => {
      makeAuthorizedRequest(url, {
          method: 'POST',
          headers: headers,
          body: data
      })
      .then(handleResponse)
      .catch((error) => {


          console.error('Request failed:', error);
          window.top.location.href = `${baseURL}/omnicast-times-up/`;
      });
  };

  // Run the request every minute (60000 ms)
  makeRequest()
  ocxInterval = setInterval(makeRequest, 60000);
}



document.getElementById("username").innerText = 'guest';

window.addEventListener("message", function(event) {
  // Check the origin of the message to ensure it's from a trusted source
  // if (event.origin !== "http://theomniverse.city")
  //     return;

  // The user's information is contained in event.data
  let data = event.data;
  userInfo = {};


  if(data && (data.event == "userInfoEvent")){
    userInfo = data.data;
  }else if(data && (data.username && data.token)){
    userInfo = data;
  }else{
    return;
  }


  username = userInfo.username;
  document.getElementById("username").innerText = username;

  email = userInfo.email;
  jwt = userInfo.token;
  refresh_token = userInfo.refresh_token.token;
  refresh_token_expires = userInfo.refresh_token.expires;

  baseURL = userInfo.baseURL
  console.log(username, email, token)
  console.log("got user info");
  console.log(userInfo.avatar);
  (document.getElementById('avatar') as HTMLImageElement).src = userInfo.avatar.replace(".glb", ".png") + '?size=256';

  if(!userInfo.avatar){
    // document.getElementById('frame').hidden = false;
    selectAvatar();

  }else{

    avatarURL = newAvatarURL || userInfo.avatar;
    // document.getElementById('frame').hidden = true;
    document.getElementById('am-container').hidden = false;
    if(data && (data.event == "userInfoEvent")){
      if(!isPlaying && window.streamPixelApp){
        // StreamPixel auto-connects, no need to manually play
        console.log('StreamPixel should already be playing');
      }

    }

  }
  // deductOCX(baseURL);

  if(userInfo.email && window.streamPixelApp){

  console.log("emitting user info event")
    type DataType = {
      action: string;
      args: {
        avatarURL: string;
        displayName: string;
        baseURL: string;
        token: string;
        onMobile?: boolean;
        sessionId?: string;
      }
    }


    let data:DataType = {
      "action":"config",
      "args":{
        "avatarURL":avatarURL,
        "displayName":username,
        "baseURL":baseURL,
        "token":jwt,
        onMobile:window.matchMedia( "(hover: none)" ).matches

      }
    }
    if(params.get('sessionId')){
      data.args['sessionId'] = params.get('sessionId');
    }
    console.log("emit event from message", data)
    emitStreamPixelEvent({
      type:"bridge",
      event:JSON.stringify(data)
    });

  }


  // Do something with the username, email, and token
}, false);

window.init = init;

// Wait for parent iframe to send "init" message, but add fallback for testing
document.addEventListener('DOMContentLoaded', () => {
  // If no parent iframe sends init within 3 seconds, auto-initialize for testing
  setTimeout(() => {
    if (!window.streamPixelApp) {
      console.log('No init message received from parent, auto-initializing for testing...');
      init();
    }
  }, 3000);
});

// deductOCX('https://theomniverse.city', 'your-jwt-token-here');
